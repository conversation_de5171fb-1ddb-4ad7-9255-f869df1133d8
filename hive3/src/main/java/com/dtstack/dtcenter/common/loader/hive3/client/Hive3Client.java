/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.dtstack.dtcenter.common.loader.hive3.client;

import com.dsg.database.datasource.vo.DbTableVO;
import com.dtstack.dtcenter.common.loader.common.DtClassConsistent;
import com.dtstack.dtcenter.common.loader.common.enums.StoredType;
import com.dtstack.dtcenter.common.loader.common.utils.DBUtil;
import com.dtstack.dtcenter.common.loader.common.utils.DelimiterUtil;
import com.dtstack.dtcenter.common.loader.common.utils.EnvUtil;
import com.dtstack.dtcenter.common.loader.common.utils.ReflectUtil;
import com.dtstack.dtcenter.common.loader.common.utils.SearchUtil;
import com.dtstack.dtcenter.common.loader.common.utils.TableUtil;
import com.dtstack.dtcenter.common.loader.hadoop.hdfs.HadoopConfUtil;
import com.dtstack.dtcenter.common.loader.hadoop.hdfs.HdfsOperator;
import com.dtstack.dtcenter.common.loader.hadoop.util.KerberosLoginUtil;
import com.dtstack.dtcenter.common.loader.hive3.HiveConnFactory;
import com.dtstack.dtcenter.common.loader.hive3.SqlConstants;
import com.dtstack.dtcenter.common.loader.hive3.downloader.HiveORCDownload;
import com.dtstack.dtcenter.common.loader.hive3.downloader.HiveParquetDownload;
import com.dtstack.dtcenter.common.loader.hive3.downloader.HiveTextDownload;
import com.dtstack.dtcenter.common.loader.rdbms.AbsRdbmsClient;
import com.dtstack.dtcenter.common.loader.rdbms.ConnFactory;
import com.dtstack.dtcenter.loader.IDownloader;
import com.dtstack.dtcenter.loader.client.ITable;
import com.dtstack.dtcenter.loader.dto.ColumnMetaDTO;
import com.dtstack.dtcenter.loader.dto.SqlQueryDTO;
import com.dtstack.dtcenter.loader.dto.Table;
import com.dtstack.dtcenter.loader.dto.TableViewDTO;
import com.dtstack.dtcenter.loader.dto.source.Hive1SourceDTO;
import com.dtstack.dtcenter.loader.dto.source.Hive3SourceDTO;
import com.dtstack.dtcenter.loader.dto.source.ISourceDTO;
import com.dtstack.dtcenter.loader.dto.source.RdbmsSourceDTO;
import com.dtstack.dtcenter.loader.enums.ConnectionClearStatus;
import com.dtstack.dtcenter.loader.enums.HiveDBDataType;
import com.dtstack.dtcenter.loader.exception.DtLoaderException;
import com.dtstack.dtcenter.loader.source.DataSourceType;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.hadoop.conf.Configuration;
import org.jetbrains.annotations.NotNull;

import java.security.PrivilegedAction;
import java.sql.Connection;
import java.sql.DatabaseMetaData;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.util.Arrays;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.Callable;
import java.util.concurrent.Future;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * @company: www.dtstack.com
 * <AUTHOR>
 * @Date ：Created in 14:03 2021/05/13
 * @Description：Hive3
 */
@Slf4j
public class Hive3Client extends AbsRdbmsClient {

    // 获取正在使用数据库
    private static final String CURRENT_DB = "select current_database()";

    // 创建库指定注释
    private static final String CREATE_DB_WITH_COMMENT = "create database %s comment '%s'";

    // 创建库
    private static final String CREATE_DB = "create database %s";

    // 模糊查询查询指定schema下的表
    private static final String TABLE_BY_SCHEMA_LIKE = "show tables in %s like '%s'";
    private static final String TABLE_BY_SCHEMA= "show tables in %s like '%s'";

    // 模糊查询database
    private static final String SHOW_DB_LIKE = "show databases like '%s'";

    // null 名称的字段名
    private static final String NULL_COLUMN = "null";

    // hive table client
    private static final ITable TABLE_CLIENT = new Hive3TableClient();

    // show tables
    private static final String SHOW_TABLE_SQL = "show tables";

    // show tables like 'xxx'
    private static final String SHOW_TABLE_LIKE_SQL = "show tables like '%s'";

    // desc db info
    private static final String DESC_DB_INFO = "desc database %s";
    // 获取当前版本号
    private static final String SHOW_VERSION = "select version()";

    // 获取最大连接数 - Hive通过配置参数获取
    private static final String GET_MAX_CONNECTIONS = "SET hive.server2.thrift.max.worker.threads";

    // 检查元数据权限 - 通过查询系统数据库来验证权限
    private static final String GET_METADATA_PRIVILEGES = "SHOW DATABASES";

    // 显示当前用户权限
    private static final String SHOW_GRANTS = "SHOW GRANT USER";
    
    // 表名正则匹配模糊查询
    private static final String SEARCH_SQL = " AND TABLE_NAME LIKE '%%%s%%' ";
    
    // 索引名模糊搜索sql
    private static final String SEARCH_INDEX_SQL = " AND INDEX_NAME LIKE '%%%s%%' ";
    
    // 函数名模糊搜索sql
    private static final String SEARCH_FUNCTION_SQL = " AND FUNCTION_NAME LIKE '%%%s%%' ";

    @Override
    protected ConnFactory getConnFactory() {
        return new HiveConnFactory();
    }

    @Override
    protected DataSourceType getSourceType() {
        return DataSourceType.HIVE3X;
    }

    @Override
    public List<String> getTableList(ISourceDTO sourceDTO, SqlQueryDTO queryDTO) {
        Integer clearStatus = beforeQuery(sourceDTO, queryDTO, false);
        Hive3SourceDTO hive3SourceDTO = (Hive3SourceDTO) sourceDTO;
        // 获取表信息需要通过show tables 语句
        String sql;
        if (Objects.nonNull(queryDTO) && StringUtils.isNotEmpty(queryDTO.getTableNamePattern())) {
            // 模糊查询
            sql = String.format(SHOW_TABLE_LIKE_SQL, addFuzzySign(queryDTO));
        } else {
            sql = SHOW_TABLE_SQL;
        }
        Statement statement = null;
        ResultSet rs = null;
        List<String> tableList = new ArrayList<>();
        try {
            statement = hive3SourceDTO.getConnection().createStatement();
            if (Objects.nonNull(queryDTO) && Objects.nonNull(queryDTO.getLimit())) {
                // 设置最大条数
                statement.setMaxRows(queryDTO.getLimit());
            }
            DBUtil.setFetchSize(statement, queryDTO);
            rs = statement.executeQuery(sql);
            int columnSize = rs.getMetaData().getColumnCount();
            while (rs.next()) {
                tableList.add(rs.getString(columnSize == 1 ? 1 : 2));
            }
        } catch (Exception e) {
            throw new DtLoaderException(String.format("get table exception,%s", e.getMessage()), e);
        } finally {
            DBUtil.closeDBResources(rs, statement, DBUtil.clearAfterGetConnection(hive3SourceDTO, clearStatus));
        }
        return SearchUtil.handleSearchAndLimit(tableList, queryDTO);
    }

    @Override
    public List<String> getTableListBySchema(ISourceDTO source, SqlQueryDTO queryDTO) {
        Hive3SourceDTO Hive3SourceDTO = (Hive3SourceDTO) source;
        if (Objects.nonNull(queryDTO) && StringUtils.isNotBlank(queryDTO.getSchema())) {
            Hive3SourceDTO.setSchema(queryDTO.getSchema());
        }
        return getTableList(Hive3SourceDTO, queryDTO);
    }

    @Override
    public String getTableMetaComment(ISourceDTO iSource, SqlQueryDTO queryDTO) {
        Integer clearStatus = beforeColumnQuery(iSource, queryDTO);
        Hive3SourceDTO hive3SourceDTO = (Hive3SourceDTO) iSource;
        try {
            return getTableMetaComment(hive3SourceDTO.getConnection(), queryDTO.getTableName());
        } finally {
            DBUtil.closeDBResources(null, null, DBUtil.clearAfterGetConnection(hive3SourceDTO, clearStatus));
        }
    }

    private String getTableMetaComment(Connection conn, String tableName) {
        Statement statement = null;
        ResultSet resultSet = null;
        try {
            statement = conn.createStatement();
            resultSet = statement.executeQuery(String.format(DtClassConsistent.HadoopConfConsistent.DESCRIBE_EXTENDED
                    , tableName));
            while (resultSet.next()) {
                String columnName = resultSet.getString(1);
                if (StringUtils.isNotEmpty(columnName) && columnName.toLowerCase().contains(DtClassConsistent.HadoopConfConsistent.TABLE_INFORMATION)) {
                    // 兼容两种数据结构 Comment: comment=
                    String string = resultSet.getString(2);
                    if (StringUtils.isNotEmpty(string) && string.contains(DtClassConsistent.HadoopConfConsistent.HIVE_COMMENT)) {
                        String[] split = string.split(DtClassConsistent.HadoopConfConsistent.HIVE_COMMENT);
                        if (split.length > 1) {
                            return split[1].split(",|}|\n")[0].trim();
                        }
                    }

                    if (StringUtils.isNotEmpty(string) && string.contains(DtClassConsistent.HadoopConfConsistent.COMMENT_WITH_COLON)) {
                        String[] split = string.split(DtClassConsistent.HadoopConfConsistent.COMMENT_WITH_COLON);
                        if (split.length > 1) {
                            return split[1].split(",|}|\n")[0].trim();
                        }
                    }
                }
            }
        } catch (Exception e) {
            throw new DtLoaderException(String.format("get table: %s's information error. Please contact the DBA to check the database、table information.",
                    tableName), e);
        } finally {
            DBUtil.closeDBResources(resultSet, statement, null);
        }
        return "";
    }

    @Override
    public List<ColumnMetaDTO> getColumnMetaData(ISourceDTO iSource, SqlQueryDTO queryDTO) {
        Integer clearStatus = beforeColumnQuery(iSource, queryDTO);
        Hive3SourceDTO hive3SourceDTO = (Hive3SourceDTO) iSource;
        try {
            return getColumnMetaData(hive3SourceDTO.getConnection(), queryDTO.getTableName(), queryDTO.getFilterPartitionColumns(),iSource,queryDTO);
        } finally {
            DBUtil.closeDBResources(null, null, DBUtil.clearAfterGetConnection(hive3SourceDTO, clearStatus));
        }
    }

    private List<ColumnMetaDTO> getColumnMetaData(Connection conn, String tableName, Boolean filterPartitionColumns,ISourceDTO iSource, SqlQueryDTO queryDTO) {
        List<ColumnMetaDTO> columnMetaDTOS = new ArrayList<>();
        Statement stmt = null;
        ResultSet resultSet = null;
        ResultSet columns = null;
        //获取schema
        String schema = queryDTO.getSchema();
        schema  = StringUtils.isBlank(schema) ? queryDTO.getDbName() : schema;
        //拼接库名和表名
        tableName = String.format("%s.%s", schema, tableName);
        try {
            stmt = conn.createStatement();
            resultSet = stmt.executeQuery("desc extended " + tableName);
            Pattern columnPattern = Pattern.compile("(\\w+)\\((\\d+)(?:,\\s*(\\d+))?\\)");
            while (resultSet.next()) {
                String dataType = resultSet.getString(DtClassConsistent.PublicConsistent.DATA_TYPE);
                String colName = resultSet.getString(DtClassConsistent.PublicConsistent.COL_NAME);
                if (StringUtils.isEmpty(dataType) || StringUtils.isBlank(colName)) {
                    break;
                }

                colName = colName.trim();
                ColumnMetaDTO metaDTO = new ColumnMetaDTO();
                metaDTO.setType(dataType.trim());
                metaDTO.setKey(colName);
                Matcher matcher = columnPattern.matcher(dataType);
                if (matcher.find()) {
                    String type = matcher.group(1);
                    String precision = matcher.group(2);
                    String scale = matcher.group(3);
                    if(StringUtils.isNotEmpty(type)){
                        metaDTO.setType(type);
                    }
                    if(StringUtils.isNotEmpty(precision)){
                        metaDTO.setPrecision(Integer.valueOf(precision));
                        metaDTO.setLength(Integer.valueOf(precision));
                    }
                    if(StringUtils.isNotEmpty(scale)){
                        metaDTO.setScale(Integer.valueOf(scale));
                    }
                }
                //通过hive元数据拿到字段长度信息
                columns = conn.getMetaData().getColumns(null, conn.getSchema(), tableName, colName);
                while (columns.next()) {
                    int columnSize = columns.getInt("COLUMN_SIZE");
                    metaDTO.setPrecision(columnSize);
                }
                metaDTO.setComment(resultSet.getString(DtClassConsistent.PublicConsistent.COMMENT));

                if (colName.startsWith("#") || "Detailed Table Information" .equals(colName)) {
                    break;
                }
                columnMetaDTOS.add(metaDTO);
            }

            DBUtil.closeDBResources(resultSet, null, null);
            resultSet = stmt.executeQuery("desc extended " + tableName);
            boolean partBegin = false;
            while (resultSet.next()) {
                String colName = resultSet.getString(DtClassConsistent.PublicConsistent.COL_NAME).trim();

                if (colName.contains("# Partition Information")) {
                    partBegin = true;
                }

                if (colName.startsWith("#")) {
                    continue;
                }

                if ("Detailed Table Information" .equals(colName)) {
                    break;
                }

                // 处理分区标志
                if (partBegin && !colName.contains("Partition Type")) {
                    Optional<ColumnMetaDTO> metaDTO =
                            columnMetaDTOS.stream().filter(meta -> colName.trim().equals(meta.getKey())).findFirst();
                    if (metaDTO.isPresent()) {
                        metaDTO.get().setPart(true);
                    }
                } else if (colName.contains("Partition Type")) {
                    //分区字段结束
                    partBegin = false;
                }
            }
            List<ColumnMetaDTO> columnMetaDataNew=new ArrayList<>();
            for (ColumnMetaDTO columnMetaDatum : columnMetaDTOS) {
                queryDTO.setColumnType(columnMetaDatum.getType());
                ColumnMetaDTO dataType = getDataType(iSource, queryDTO);
                if(Objects.nonNull(dataType)){
                    columnMetaDatum.setDataType(dataType.getDataType());
                }
                columnMetaDataNew.add(columnMetaDatum);

            }
            return columnMetaDataNew.stream().filter(column -> !filterPartitionColumns || !column.getPart()).collect(Collectors.toList());
        } catch (SQLException e) {
            throw new DtLoaderException(String.format("Failed to get meta information for the fields of table :%s. Please contact the DBA to check the database table information.",
                    tableName), e);
        } finally {
            DBUtil.closeDBResources(resultSet, stmt, null);
            DBUtil.closeDBResources(columns, null, null);
        }
    }

    @Override
    public ColumnMetaDTO getDataType(ISourceDTO source, SqlQueryDTO queryDTO) {
        ColumnMetaDTO columnMetaDTO=new ColumnMetaDTO();
        try{
            String columnType = queryDTO.getColumnType().toUpperCase();
            if(columnType.contains(HiveDBDataType.VARCHAR.name())){
                columnType=HiveDBDataType.VARCHAR.name();
            }
            HiveDBDataType dataType = HiveDBDataType.getDataType(columnType.toUpperCase());
            columnMetaDTO.setDataType(dataType.getDataType());
        }catch (Exception e){
            return columnMetaDTO;
        }

        return columnMetaDTO;
    }



    @Override
    public Boolean testCon(ISourceDTO sourceDTO) {
        Future<Boolean> future = null;
        try {
            // 使用线程池的方式来控制连通超时
            Callable<Boolean> call = () -> testConnection(sourceDTO);
            future = executor.submit(call);
            // 如果在设定超时(以秒为单位)之内，还没得到连通性测试结果，则认为连通性测试连接超时，不继续阻塞
            return future.get(EnvUtil.getTestConnTimeout(), TimeUnit.SECONDS);
        } catch (TimeoutException e) {
            throw new DtLoaderException(String.format("Test connect timeout！,%s", e.getMessage()), e);
        } catch (Exception e) {
            if (e instanceof DtLoaderException) {
                throw new DtLoaderException(e.getMessage(), e);
            }
            if (e.getCause() != null && e.getCause() instanceof DtLoaderException) {
                throw new DtLoaderException(e.getCause().getMessage(), e);
            }
            throw new DtLoaderException(e.getMessage(), e);
        } finally {
            if (Objects.nonNull(future)) {
                future.cancel(true);
            }
        }
    }

    private Boolean testConnection(ISourceDTO iSource) {
        // 先校验数据源连接性
        Boolean testCon = super.testCon(iSource);
        if (!testCon) {
            return Boolean.FALSE;
        }
        Hive3SourceDTO Hive3SourceDTO = (Hive3SourceDTO) iSource;
        if (StringUtils.isBlank(Hive3SourceDTO.getDefaultFS())) {
            return Boolean.TRUE;
        }

        return HdfsOperator.checkConnection(Hive3SourceDTO.getDefaultFS(), Hive3SourceDTO.getConfig(), Hive3SourceDTO.getKerberosConfig());
    }

    @Override
    public IDownloader getDownloader(ISourceDTO sourceDTO, SqlQueryDTO queryDTO) {
        Hive3SourceDTO hive3SourceDTO = (Hive3SourceDTO) sourceDTO;
        Integer clearStatus = beforeQuery(hive3SourceDTO, queryDTO, false);
        Table table;
        // 普通字段集合
        ArrayList<ColumnMetaDTO> commonColumn = new ArrayList<>();
        // 分区字段集合
        ArrayList<String> partitionColumns = new ArrayList<>();
        // 分区表所有分区 如果为 null 标识不是分区表，如果为空标识分区表无分区
        List<String> partitions = null;
        try {
            // 获取表详情信息
            table = getTable(hive3SourceDTO, queryDTO);
            for (ColumnMetaDTO columnMetaDatum : table.getColumns()) {
                // 非分区字段
                if (columnMetaDatum.getPart()) {
                    partitionColumns.add(columnMetaDatum.getKey());
                    continue;
                }
                commonColumn.add(columnMetaDatum);
            }
            // 分区表
            if (CollectionUtils.isNotEmpty(partitionColumns)) {
                partitions = TABLE_CLIENT.showPartitions(hive3SourceDTO, queryDTO.getTableName());
                if (CollectionUtils.isNotEmpty(partitions)) {
                    // 转化成小写，因为分区字段即使是大写在 hdfs 上仍是小写存在
                    partitions = partitions.stream()
                            .filter(StringUtils::isNotEmpty)
                            .map(String::toLowerCase)
                            .collect(Collectors.toList());
                }
            }
        } catch (Exception e) {
            throw new DtLoaderException(String.format("failed to get table detail: %s", e.getMessage()), e);
        } finally {
            DBUtil.clearAfterGetConnection(hive3SourceDTO, clearStatus);
        }
        // 查询的字段列表，支持按字段获取数据
        List<String> columns = queryDTO.getColumns();
        // 需要的字段索引（包括分区字段索引）
        List<Integer> needIndex = Lists.newArrayList();
        // columns字段不为空且不包含*时获取指定字段的数据
        if (CollectionUtils.isNotEmpty(columns) && !columns.contains("*")) {
            // 保证查询字段的顺序!
            for (String column : columns) {
                if (NULL_COLUMN.equalsIgnoreCase(column)) {
                    needIndex.add(Integer.MAX_VALUE);
                    continue;
                }
                // 判断查询字段是否存在
                boolean check = false;
                for (int j = 0; j < table.getColumns().size(); j++) {
                    if (column.equalsIgnoreCase(table.getColumns().get(j).getKey())) {
                        needIndex.add(j);
                        check = true;
                        break;
                    }
                }
                if (!check) {
                    throw new DtLoaderException("The query field does not exist! Field name：" + column);
                }
            }
        }

        // 校验高可用配置
        if (StringUtils.isBlank(hive3SourceDTO.getDefaultFS())) {
            throw new DtLoaderException("defaultFS incorrect format");
        }
        transformDelim(table);
        Configuration conf = HadoopConfUtil.getHdfsConf(hive3SourceDTO.getDefaultFS(), hive3SourceDTO.getConfig(), hive3SourceDTO.getKerberosConfig());
        List<String> finalPartitions = partitions;
        return KerberosLoginUtil.loginWithUGI(hive3SourceDTO.getKerberosConfig()).doAs(
                (PrivilegedAction<IDownloader>) () -> {
                    try {
                        return createDownloader(table.getStoreType(), conf, table.getPath(), commonColumn, table.getDelim(), partitionColumns, needIndex, queryDTO.getPartitionColumns(), finalPartitions, hive3SourceDTO.getKerberosConfig());
                    } catch (Exception e) {
                        throw new DtLoaderException(String.format("create downloader exception,%s", e.getMessage()), e);
                    }
                }
        );
    }

    /**
     * 获取hdfs 里真正的切分符
     *
     * @param table
     */
    private void transformDelim(Table table) {
        if (StringUtils.isEmpty(table.getDelim())) {
            return;
        }
        Boolean isLazySimpleSerDe = ReflectUtil.fieldExists(Table.class, "isLazySimpleSerDe") ? table.getIsLazySimpleSerDe() : true;
        String fieldDelimiter = table.getDelim();
        String finalFieldDelimiter = isLazySimpleSerDe ? (fieldDelimiter.charAt(0) == '\\' ? fieldDelimiter.substring(0, 2) : fieldDelimiter.substring(0, 1)) : fieldDelimiter;
        table.setDelim(finalFieldDelimiter);
    }

    /**
     * 根据存储格式创建对应的hiveDownloader
     *
     * @param storageMode      存储格式
     * @param conf             配置
     * @param tableLocation    表hdfs路径
     * @param columns          字段集合
     * @param fieldDelimiter   textFile 表列分隔符
     * @param partitionColumns 分区字段集合
     * @param needIndex        需要查询的字段索引位置
     * @param filterPartitions 需要查询的分区
     * @param partitions       全部分区
     * @param kerberosConfig   kerberos 配置
     * @return downloader
     * @throws Exception 异常信息
     */
    private @NotNull IDownloader createDownloader(String storageMode, Configuration conf, String tableLocation,
                                                  List<ColumnMetaDTO> columns, String fieldDelimiter,
                                                  ArrayList<String> partitionColumns, List<Integer> needIndex,
                                                  Map<String, String> filterPartitions, List<String> partitions,
                                                  Map<String, Object> kerberosConfig) throws Exception {
        // 根据存储格式创建对应的hiveDownloader
        if (StringUtils.isBlank(storageMode)) {
            throw new DtLoaderException("Hive table reads for this storage type are not supported");
        }

        List<String> columnNames = columns.stream().map(ColumnMetaDTO::getKey).collect(Collectors.toList());
        if (StringUtils.containsIgnoreCase(storageMode, "text")) {
            HiveTextDownload hiveTextDownload = new HiveTextDownload(conf, tableLocation, columnNames,
                    fieldDelimiter, partitionColumns, filterPartitions, needIndex, partitions, kerberosConfig);
            hiveTextDownload.configure();
            return hiveTextDownload;
        }

        if (StringUtils.containsIgnoreCase(storageMode, "orc")) {
            HiveORCDownload hiveORCDownload = new HiveORCDownload(conf, tableLocation, columnNames,
                    partitionColumns, needIndex, partitions, kerberosConfig);
            hiveORCDownload.configure();
            return hiveORCDownload;
        }

        if (StringUtils.containsIgnoreCase(storageMode, "parquet")) {
            HiveParquetDownload hiveParquetDownload = new HiveParquetDownload(conf, tableLocation, columns,
                    partitionColumns, needIndex, filterPartitions, partitions, kerberosConfig);
            hiveParquetDownload.configure();
            return hiveParquetDownload;
        }

        throw new DtLoaderException("Hive table reads for this storage type are not supported");
    }

    /**
     * 处理hive分区信息和sql语句
     *
     * @param sqlQueryDTO 查询条件
     * @return
     */
    @Override
    public String dealSql(ISourceDTO iSourceDTO, SqlQueryDTO sqlQueryDTO) {
        Map<String, String> partitions = sqlQueryDTO.getPartitionColumns();
        StringBuilder partSql = new StringBuilder();
        //拼接分区信息
        if (MapUtils.isNotEmpty(partitions)) {
            boolean check = true;
            partSql.append(" where ");
            Set<String> set = partitions.keySet();
            for (String column : set) {
                if (check) {
                    partSql.append(column + "=").append(partitions.get(column));
                    check = false;
                } else {
                    partSql.append(" and ").append(column + "=").append(partitions.get(column));
                }
            }
        }

        return "select * from " + transferSchemaAndTableName(sqlQueryDTO.getSchema(), sqlQueryDTO.getTableName()) + partSql.toString() + limitSql(sqlQueryDTO.getPreviewNum());
    }

    @Override
    protected String transferSchemaAndTableName(String schema, String tableName) {
        if (!tableName.startsWith("`") || !tableName.endsWith("`")) {
            tableName = String.format("`%s`", tableName);
        }
        if (StringUtils.isBlank(schema)) {
            return tableName;
        }
        if (!schema.startsWith("`") || !schema.endsWith("`")) {
            schema = String.format("`%s`", schema);
        }
        return String.format("%s.%s", schema, tableName);
    }

    @Override
    public List<ColumnMetaDTO> getPartitionColumn(ISourceDTO source, SqlQueryDTO queryDTO) {
        List<ColumnMetaDTO> columnMetaDTOS = getColumnMetaData(source, queryDTO);
        List<ColumnMetaDTO> partitionColumnMeta = new ArrayList<>();
        columnMetaDTOS.forEach(columnMetaDTO -> {
            if (columnMetaDTO.getPart()) {
                partitionColumnMeta.add(columnMetaDTO);
            }
        });
        return partitionColumnMeta;
    }

    @Override
    public Table getTable(ISourceDTO source, SqlQueryDTO queryDTO) {
        Integer clearStatus = beforeColumnQuery(source, queryDTO);
        Hive3SourceDTO hive3SourceDTO = (Hive3SourceDTO) source;

        Table tableInfo = new Table();
        try {
            tableInfo.setName(queryDTO.getTableName());
            // 获取表注释
            tableInfo.setComment(getTableMetaComment(hive3SourceDTO.getConnection(), queryDTO.getTableName()));
            // 先获取全部字段，再过滤
            List<ColumnMetaDTO> columnMetaDTOS = getColumnMetaData(hive3SourceDTO.getConnection(), queryDTO.getTableName(), false,source,queryDTO);
            // 分区字段不为空表示是分区表
            if (ReflectUtil.fieldExists(Table.class, "isPartitionTable")) {
                tableInfo.setIsPartitionTable(CollectionUtils.isNotEmpty(TableUtil.getPartitionColumns(columnMetaDTOS)));
            }
            tableInfo.setColumns(TableUtil.filterPartitionColumns(columnMetaDTOS, queryDTO.getFilterPartitionColumns()));
            // 获取表结构信息
            getTable(tableInfo, hive3SourceDTO, queryDTO.getTableName());
        } catch (Exception e) {
            throw new DtLoaderException(String.format("SQL executed exception, %s", e.getMessage()), e);
        } finally {
            DBUtil.closeDBResources(null, null, DBUtil.clearAfterGetConnection(hive3SourceDTO, clearStatus));
        }
        return tableInfo;
    }

    private void getTable(Table tableInfo, Hive3SourceDTO hive3SourceDTO, String tableName) {
        List<Map<String, Object>> result = executeQuery(hive3SourceDTO, SqlQueryDTO.builder().sql("desc formatted " + tableName).build(), ConnectionClearStatus.NORMAL.getValue());
        boolean isTableInfo = false;
        for (Map<String, Object> row : result) {
            String colName = MapUtils.getString(row, "col_name", "");
            String comment = MapUtils.getString(row, "comment", "");
            String dataTypeOrigin = MapUtils.getString(row, "data_type", "");
            if (StringUtils.isBlank(colName) || StringUtils.isBlank(dataTypeOrigin)) {
                if (StringUtils.isNotBlank(colName) && colName.contains("# Detailed Table Information")) {
                    isTableInfo = true;
                }
            }
            // 去空格处理
            String dataType = dataTypeOrigin.trim();
            if (!isTableInfo) {
                continue;
            }

            if (colName.contains("Location")) {
                tableInfo.setPath(dataType);
                continue;
            }

            if (colName.contains("Table Type")) {
                if (ReflectUtil.fieldExists(Table.class, "isView")) {
                    tableInfo.setIsView(StringUtils.containsIgnoreCase(dataType, "VIEW"));
                }
                tableInfo.setExternalOrManaged(dataType);
                continue;
            }

            // 兼容一下返回值 Type 的情况
            if (("Type".equals(colName.trim()) || "Type:".equals(colName.trim())) && StringUtils.isEmpty(tableInfo.getExternalOrManaged())) {
                if (ReflectUtil.fieldExists(Table.class, "isView")) {
                    tableInfo.setIsView(StringUtils.containsIgnoreCase(dataType, "VIEW"));
                }
                tableInfo.setExternalOrManaged(dataType);
                continue;
            }

            if (colName.contains("field.delim")) {
                tableInfo.setDelim(DelimiterUtil.charAtIgnoreEscape(dataTypeOrigin));
                continue;
            }

            if (dataType.contains("field.delim")) {
                String delimit = MapUtils.getString(row, "comment", "");
                tableInfo.setDelim(DelimiterUtil.charAtIgnoreEscape(delimit));
                continue;
            }

            if (colName.contains("Owner")) {
                tableInfo.setOwner(dataType);
                continue;
            }

            if (colName.contains("CreateTime") || colName.contains("CreatedTime")) {
                tableInfo.setCreatedTime(dataType);
                continue;
            }

            if (colName.contains("LastAccess")) {
                tableInfo.setLastAccess(dataType);
                continue;
            }

            if (colName.contains("CreatedBy")) {
                tableInfo.setCreatedBy(dataType);
                continue;
            }

            if (colName.contains("Database")) {
                tableInfo.setDb(dataType);
                continue;
            }

            if (StringUtils.containsIgnoreCase(dataType, "transactional")) {
                if (ReflectUtil.fieldExists(Table.class, "isTransTable") && StringUtils.containsIgnoreCase(comment, "true")) {
                    tableInfo.setIsTransTable(true);
                }
                continue;
            }

            if (tableInfo.getStoreType() == null && colName.contains("InputFormat")) {
                for (StoredType hiveStoredType : StoredType.values()) {
                    if (dataType.contains(hiveStoredType.getInputFormatClass())) {
                        tableInfo.setStoreType(hiveStoredType.getValue());
                        break;
                    }
                }
            }

            //单字符作为分隔符 org.apache.hadoop.hive.serde2.lazy.LazySimpleSerDe
            //多字符作为分隔符  org.apache.hadoop.hive.contrib.serde2.MultiDelimitSerDe,org.apache.hadoop.hive.contrib.serde2.RegexSerDe
            if (colName.contains("SerDe Library")) {
                if (ReflectUtil.fieldExists(Table.class, "isLazySimpleSerDe")) {
                    if (StringUtils.containsIgnoreCase(dataType, "LazySimpleSerDe")) {
                        tableInfo.setIsTransTable(true);
                    } else {
                        tableInfo.setIsTransTable(false);
                    }
                }
            }
        }
        // text 未获取到分隔符情况下添加默认值
        if (StringUtils.equalsIgnoreCase(StoredType.TEXTFILE.getValue(), tableInfo.getStoreType()) && Objects.isNull(tableInfo.getDelim())) {
            tableInfo.setDelim(DtClassConsistent.HiveConsistent.DEFAULT_FIELD_DELIMIT);
        }
    }

    @Override
    protected String getCurrentDbSql() {
        return CURRENT_DB;
    }

    @Override
    protected String getCreateDatabaseSql(String dbName, String comment) {
        return StringUtils.isBlank(comment) ? String.format(CREATE_DB, dbName) : String.format(CREATE_DB_WITH_COMMENT, dbName, comment);
    }

    @Override
    public Boolean isDatabaseExists(ISourceDTO source, String dbName) {
        if (StringUtils.isBlank(dbName)) {
            throw new DtLoaderException("database name cannot be empty!");
        }
        return CollectionUtils.isNotEmpty(executeQuery(source, SqlQueryDTO.builder().sql(String.format(SHOW_DB_LIKE, dbName)).build()));
    }


    @Override
    public String getTableExistSql(ISourceDTO source,SqlQueryDTO queryDTO) {
        String sql = String.format(TABLE_BY_SCHEMA, queryDTO.getDbName(), queryDTO.getTableName());
        return sql;
    }
    @Override
    public String getCreateTableSql(ISourceDTO source, SqlQueryDTO queryDTO) {
        Integer clearStatus = beforeQuery(source, queryDTO, false);
        RdbmsSourceDTO rdbmsSourceDTO = (RdbmsSourceDTO) source;
        // 获取表信息需要通过show databases 语句
        String tableName ;
        if (StringUtils.isNotEmpty(rdbmsSourceDTO.getSchema())) {
            tableName = String.format("%s.%s", rdbmsSourceDTO.getSchema(), queryDTO.getTableName());
        } else {
            tableName = queryDTO.getTableName();
        }
        String sql = String.format("show create table %s", tableName);
        Statement statement = null;
        ResultSet rs = null;
        StringBuilder createTableSql = new StringBuilder();
        try {
            statement = rdbmsSourceDTO.getConnection().createStatement();
            rs = statement.executeQuery(sql);
            while (rs.next()) {
                createTableSql.append(rs.getString(1));
            }
        } catch (Exception e) {
            throw new DtLoaderException(String.format("failed to get the create table sql：%s", e.getMessage()), e);
        } finally {
            DBUtil.closeDBResources(rs, statement, DBUtil.clearAfterGetConnection(rdbmsSourceDTO, clearStatus));
        }
        return createTableSql.toString();
    }

    @Override
    public String getDescDbSql(String dbName) {
        return String.format(DESC_DB_INFO, dbName);
    }

    @Override
    protected String getFuzzySign() {
        return "*";
    }

    @Override
    protected Pair<Character, Character> getSpecialSign() {
        return Pair.of('`', '`');
    }

    @Override
    protected String getVersionSql() {
        return SHOW_VERSION;
    }

    @Override
    public List<TableViewDTO> getTableAndViewList(ISourceDTO iSource, SqlQueryDTO queryDTO) {
        List<String> tableList = getTableList(iSource, queryDTO);
        List<TableViewDTO> tableViewDTOS=new ArrayList<>();
        for (String s : tableList) {
            TableViewDTO tableViewDTO = new TableViewDTO(s, "TABLE");
            tableViewDTOS.add(tableViewDTO);
        }
        return tableViewDTOS;
    }

    /**
     * 获取表或视图元数据
     * @param sourceDTO 数据源信息
     * @param queryDTO 查询参数
     * @return 表或视图列表
     */
    @Override
    public List<DbTableVO> getMedataDataTables(ISourceDTO sourceDTO, SqlQueryDTO queryDTO) {
        Hive3SourceDTO hive3SourceDTO = (Hive3SourceDTO) sourceDTO;
        Integer clearStatus = beforeQuery(sourceDTO, queryDTO, false);
        List<DbTableVO> dbTableVOS = new ArrayList<>();

        try {
            // 首先尝试使用DatabaseMetaData.getTables()方法，这是最高效的方式
            DatabaseMetaData metaData = hive3SourceDTO.getConnection().getMetaData();
            String dbName = StringUtils.isNotBlank(queryDTO.getDbName()) ? queryDTO.getDbName() : "default";

            // 设置表类型过滤
            String[] tableTypes = null;
            if (StringUtils.isNotEmpty(queryDTO.getType())) {
                if (SqlConstants.VIEW_TYPE.equalsIgnoreCase(queryDTO.getType())) {
                    tableTypes = new String[]{"VIEW"};
                } else if (SqlConstants.TABLE_TYPE.equalsIgnoreCase(queryDTO.getType())) {
                    tableTypes = new String[]{"TABLE"};
                }
            } else {
                tableTypes = new String[]{"TABLE", "VIEW"};
            }

            // 设置表名模式
            String tableNamePattern = null;
            if (StringUtils.isNotEmpty(queryDTO.getTableNamePattern())) {
                tableNamePattern = "%" + queryDTO.getTableNamePattern() + "%";
            }

            log.info("getMedataDataTables - 使用DatabaseMetaData.getTables(), schema: {}, pattern: {}, types: {}",
                    dbName, tableNamePattern, Arrays.toString(tableTypes));

            try (ResultSet resultSet = metaData.getTables(null, dbName, tableNamePattern, tableTypes)) {
                while (resultSet.next()) {
                    DbTableVO dbTableVO = new DbTableVO();
                    dbTableVO.setDbName(dbName);
                    dbTableVO.setSchemaName(dbName);
                    dbTableVO.setName(resultSet.getString("TABLE_NAME"));
                    dbTableVO.setType(resultSet.getString("TABLE_TYPE"));
                    dbTableVOS.add(dbTableVO);
                }
            }

            log.info("getMedataDataTables - 通过DatabaseMetaData获取到 {} 个表/视图", dbTableVOS.size());

        } catch (Exception e) {
            log.warn("使用DatabaseMetaData.getTables()失败，回退到传统方式: {}", e.getMessage());
            // 如果DatabaseMetaData方式失败，回退到传统的DESCRIBE FORMATTED方式
            return getMedataDataTablesWithDescribe(sourceDTO, queryDTO);
        } finally {
            DBUtil.clearAfterGetConnection(hive3SourceDTO, clearStatus);
        }

        return dbTableVOS;
    }

    /**
     * 使用DESCRIBE FORMATTED方式获取表或视图元数据（回退方案）
     */
    private List<DbTableVO> getMedataDataTablesWithDescribe(ISourceDTO sourceDTO, SqlQueryDTO queryDTO) {
        Hive3SourceDTO hive3SourceDTO = (Hive3SourceDTO) sourceDTO;
        Integer clearStatus = beforeQuery(sourceDTO, queryDTO, false);
        Statement statement = null;
        ResultSet resultSet = null;
        List<DbTableVO> dbTableVOS = new ArrayList<>();

        try {
            statement = hive3SourceDTO.getConnection().createStatement();
            String dbName = StringUtils.isNotBlank(queryDTO.getDbName()) ? queryDTO.getDbName() : "default";

            // 首先获取所有表名
            String showTablesSQL = "";
            if (StringUtils.isNotEmpty(queryDTO.getTableNamePattern())) {
                String pattern = String.format(SqlConstants.SEARCH_SQL, queryDTO.getTableNamePattern());
                showTablesSQL = String.format(SqlConstants.SHOW_TABLE_BY_SCHEMA_LIKE_SQL_NEW, dbName, pattern);
            } else {
                showTablesSQL = String.format(SqlConstants.SHOW_TABLE_BY_SCHEMA_SQL_NEW, dbName);
            }

            log.info("getMedataDataTablesWithDescribe - 获取表列表 SQL: {}", showTablesSQL);
            resultSet = statement.executeQuery(showTablesSQL);

            List<String> tableNames = new ArrayList<>();
            while (resultSet.next()) {
                tableNames.add(resultSet.getString(1));
            }
            DBUtil.closeDBResources(resultSet, null, null);

            // 对每个表进行类型判断
            for (String tableName : tableNames) {
                String describeSQL = String.format(SqlConstants.DESCRIBE_FORMATTED_SQL, tableName);
                log.debug("getMedataDataTablesWithDescribe - 检查表类型 SQL: {}", describeSQL);

                try {
                    resultSet = statement.executeQuery(describeSQL);
                    String tableType = "TABLE"; // 默认为表

                    while (resultSet.next()) {
                        String colName = resultSet.getString("col_name");
                        String dataType = resultSet.getString("data_type");

                        if (StringUtils.containsIgnoreCase(colName, "Table Type")) {
                            if (StringUtils.containsIgnoreCase(dataType, "VIEW")) {
                                tableType = "VIEW";
                            }
                            break;
                        }
                        // 兼容一下返回值 Type 的情况
                        if (("Type".equals(colName.trim()) || "Type:".equals(colName.trim()))) {
                            if (StringUtils.containsIgnoreCase(dataType, "VIEW")) {
                                tableType = "VIEW";
                            }
                            break;
                        }
                    }
                    DBUtil.closeDBResources(resultSet, null, null);

                    // 根据查询类型过滤
                    boolean shouldInclude = true;
                    if (StringUtils.isNotEmpty(queryDTO.getType())) {
                        if (SqlConstants.VIEW_TYPE.equalsIgnoreCase(queryDTO.getType()) && !"VIEW".equals(tableType)) {
                            shouldInclude = false;
                        } else if (SqlConstants.TABLE_TYPE.equalsIgnoreCase(queryDTO.getType()) && "VIEW".equals(tableType)) {
                            shouldInclude = false;
                        }
                    }

                    if (shouldInclude) {
                        DbTableVO dbTableVO = new DbTableVO();
                        dbTableVO.setDbName(dbName);
                        dbTableVO.setSchemaName(dbName);
                        dbTableVO.setName(tableName);
                        dbTableVO.setType(tableType);
                        dbTableVOS.add(dbTableVO);
                    }

                } catch (Exception e) {
                    log.warn("获取表 {} 类型信息失败: {}", tableName, e.getMessage());
                    // 如果获取类型失败，默认作为表处理
                    if (StringUtils.isEmpty(queryDTO.getType()) || SqlConstants.TABLE_TYPE.equalsIgnoreCase(queryDTO.getType())) {
                        DbTableVO dbTableVO = new DbTableVO();
                        dbTableVO.setDbName(dbName);
                        dbTableVO.setSchemaName(dbName);
                        dbTableVO.setName(tableName);
                        dbTableVO.setType(SqlConstants.TABLE_TYPE);
                        dbTableVOS.add(dbTableVO);
                    }
                }
            }

        } catch (Exception e) {
            throw new DtLoaderException(String.format("获取Hive3表或视图元数据异常: %s", e.getMessage()), e);
        } finally {
            DBUtil.closeDBResources(resultSet, statement, DBUtil.clearAfterGetConnection(hive3SourceDTO, clearStatus));
        }

        return dbTableVOS;
    }

    /**
     * 获取索引列表 - Hive 3.x已移除索引功能
     * @param sourceDTO 数据源信息
     * @param queryDTO 查询参数
     * @return 索引列表
     */
    @Override
    public List<DbTableVO> getIndexList(ISourceDTO sourceDTO, SqlQueryDTO queryDTO) {
        // Hive 3.x已移除索引功能，返回空列表
        log.warn("Hive 3.x已移除索引功能，返回空列表");
        return new ArrayList<>();
    }

    /**
     * 获取索引字段 - Hive 3.x已移除索引功能
     * @param sourceDTO 数据源信息
     * @param queryDTO 查询参数
     * @return 索引字段列表
     */
    @Override
    public List<ColumnMetaDTO> getIndexColumn(ISourceDTO sourceDTO, SqlQueryDTO queryDTO) {
        // Hive 3.x已移除索引功能，返回空列表
        log.warn("Hive 3.x已移除索引功能，返回空列表");
        return new ArrayList<>();
    }

    /**
     * 获取函数列表
     * @param sourceDTO 数据源信息
     * @param queryDTO 查询参数
     * @return 函数列表
     */
    @Override
    public List<DbTableVO> getFunctionList(ISourceDTO sourceDTO, SqlQueryDTO queryDTO) {
        Hive3SourceDTO hiveSourceDTO = (Hive3SourceDTO) sourceDTO;
        Integer clearStatus = beforeQuery(sourceDTO, queryDTO, false);
        Statement statement = null;
        ResultSet resultSet = null;
        List<DbTableVO> functionMetaDTOS = new ArrayList<>();
        try {
            // Hive不支持存储过程，只支持函数
            if (SqlConstants.PROCEDURE_TYPE.equalsIgnoreCase(queryDTO.getType())) {
                log.warn("Hive 3.x不支持存储过程，返回空列表");
                return functionMetaDTOS;
            }
            statement = hiveSourceDTO.getConnection().createStatement();
            String sql = "";
            //在Hive中主要是内置函数
            sql = "SHOW FUNCTIONS";
            if (StringUtils.isNotEmpty(queryDTO.getTableNamePattern())) {
                sql += " LIKE '%" + queryDTO.getTableNamePattern() + "%'";
            }

            log.info("getFunctionList sql:{}", sql);
            resultSet = statement.executeQuery(sql);
            while (resultSet.next()) {
                DbTableVO functionMetaDTO = new DbTableVO();
                functionMetaDTO.setDbName(queryDTO.getDbName());
                functionMetaDTO.setSchemaName(queryDTO.getSchema());
                functionMetaDTO.setName(resultSet.getString(1)); // 函数名称在第一列
                functionMetaDTO.setType("FUNCTION");
                functionMetaDTOS.add(functionMetaDTO);
            }
        } catch (Exception e) {
            throw new DtLoaderException(String.format("获取函数异常:%s", e.getMessage()), e);
        } finally {
            DBUtil.closeDBResources(resultSet, statement, DBUtil.clearAfterGetConnection(hiveSourceDTO, clearStatus));
        }
        return functionMetaDTOS;
    }

    /**
     * 获取函数参数
     * @param sourceDTO 数据源信息
     * @param queryDTO 查询参数
     * @return 函数参数列表
     */
    @Override
    public List<ColumnMetaDTO> getFunctionArguments(ISourceDTO sourceDTO, SqlQueryDTO queryDTO) {
        Hive3SourceDTO hive3SourceDTO = (Hive3SourceDTO) sourceDTO;
        Integer clearStatus = beforeQuery(sourceDTO, queryDTO, false);
        List<ColumnMetaDTO> argumentList = new ArrayList<>();

        try {
            String functionName = queryDTO.getObjectName();
            if (StringUtils.isEmpty(functionName)) {
                throw new DtLoaderException("函数名不能为空");
            }

            // 首先尝试使用DatabaseMetaData.getFunctionColumns()方法
            DatabaseMetaData metaData = hive3SourceDTO.getConnection().getMetaData();
            String dbName = StringUtils.isNotBlank(queryDTO.getDbName()) ? queryDTO.getDbName() : "default";

            log.info("getFunctionArguments - 使用DatabaseMetaData.getFunctionColumns(), schema: {}, function: {}",
                    dbName, functionName);

            try (ResultSet resultSet = metaData.getFunctionColumns(null, dbName, functionName, null)) {
                while (resultSet.next()) {
                    ColumnMetaDTO argumentDTO = new ColumnMetaDTO();
                    argumentDTO.setKey(resultSet.getString("COLUMN_NAME"));
                    argumentDTO.setType(resultSet.getString("TYPE_NAME"));

                    // 设置参数类型（输入、输出、返回值等）
                    int columnType = resultSet.getInt("COLUMN_TYPE");
                    switch (columnType) {
                        case DatabaseMetaData.functionColumnIn:
                            argumentDTO.setComment("INPUT");
                            break;
                        case DatabaseMetaData.functionColumnOut:
                            argumentDTO.setComment("OUTPUT");
                            break;
                        case DatabaseMetaData.functionColumnInOut:
                            argumentDTO.setComment("INOUT");
                            break;
                        case DatabaseMetaData.functionReturn:
                            argumentDTO.setComment("RETURN");
                            break;
                        default:
                            argumentDTO.setComment("UNKNOWN");
                    }

                    // 设置其他属性
                    try {
                        argumentDTO.setPrecision(resultSet.getInt("PRECISION"));
                        argumentDTO.setScale(resultSet.getInt("SCALE"));
                    } catch (Exception e) {
                        // 忽略获取精度和标度失败的情况
                    }

                    argumentList.add(argumentDTO);
                }
            }

            log.info("getFunctionArguments - 通过DatabaseMetaData获取到 {} 个函数参数", argumentList.size());

        } catch (Exception e) {
            log.warn("使用DatabaseMetaData.getFunctionColumns()失败，回退到传统方式: {}", e.getMessage());
            // 如果DatabaseMetaData方式失败，回退到传统的DESCRIBE FUNCTION方式
            return getFunctionArgumentsWithDescribe(sourceDTO, queryDTO);
        } finally {
            DBUtil.clearAfterGetConnection(hive3SourceDTO, clearStatus);
        }

        return argumentList;
    }

    /**
     * 使用DESCRIBE FUNCTION方式获取函数参数（回退方案）
     */
    private List<ColumnMetaDTO> getFunctionArgumentsWithDescribe(ISourceDTO sourceDTO, SqlQueryDTO queryDTO) {
        Hive3SourceDTO hive3SourceDTO = (Hive3SourceDTO) sourceDTO;
        Integer clearStatus = beforeQuery(sourceDTO, queryDTO, false);
        Statement statement = null;
        ResultSet resultSet = null;
        List<ColumnMetaDTO> argumentList = new ArrayList<>();

        try {
            statement = hive3SourceDTO.getConnection().createStatement();
            String functionName = queryDTO.getObjectName();
            if (StringUtils.isEmpty(functionName)) {
                throw new DtLoaderException("函数名不能为空");
            }

            String sql = String.format(SqlConstants.SHOW_FUNCTION_ARGUMENTS_BY_SCHEMA_SQL_NEW, functionName);
            log.info("getFunctionArgumentsWithDescribe SQL: {}", sql);
            resultSet = statement.executeQuery(sql);

            // DESCRIBE FUNCTION EXTENDED返回的是函数的详细描述
            while (resultSet.next()) {
                String description = resultSet.getString(1);
                ColumnMetaDTO argumentDTO = new ColumnMetaDTO();
                argumentDTO.setKey("description");
                argumentDTO.setType("string");
                argumentDTO.setComment(description);
                argumentList.add(argumentDTO);
            }

        } catch (Exception e) {
            throw new DtLoaderException(String.format("获取Hive3函数参数异常: %s", e.getMessage()), e);
        } finally {
            DBUtil.closeDBResources(resultSet, statement, DBUtil.clearAfterGetConnection(hive3SourceDTO, clearStatus));
        }

        return argumentList;
    }

    @Override
    public Integer getMaxConnections(ISourceDTO sourceDTO) {
        Integer result = null;
        try {
            List<Map<String, Object>> resultList = executeQuery(sourceDTO, SqlQueryDTO.builder().sql(GET_MAX_CONNECTIONS).build());
            if (CollectionUtils.isNotEmpty(resultList)) {
                // Hive返回的是配置参数，需要解析值
                Map<String, Object> configMap = resultList.get(0);
                if (MapUtils.isNotEmpty(configMap)) {
                    // 尝试从不同可能的字段名获取值
                    Object value = configMap.get("value");
                    if (value == null) {
                        value = configMap.get("set");
                    }
                    if (value == null) {
                        value = configMap.get("hive.server2.thrift.max.worker.threads");
                    }
                    if (value != null) {
                        String valueStr = String.valueOf(value);
                        // 解析配置值，格式可能是 "key=value"
                        if (valueStr.contains("=")) {
                            String[] parts = valueStr.split("=");
                            if (parts.length > 1) {
                                try {
                                    result = Integer.parseInt(parts[1].trim());
                                } catch (NumberFormatException e) {
                                    log.warn("Failed to parse max connections value: {}", parts[1]);
                                }
                            }
                        } else {
                            try {
                                result = Integer.parseInt(valueStr.trim());
                            } catch (NumberFormatException e) {
                                log.warn("Failed to parse max connections value: {}", valueStr);
                            }
                        }
                    }
                }
            }
        } catch (Exception e) {
            log.error("get max connections error", e);
        }
        return result;
    }

    @Override
    public Boolean getMetadataPrivileges(ISourceDTO sourceDTO) {
        Boolean result = null;
        Hive3SourceDTO hiveSourceDTO = (Hive3SourceDTO) sourceDTO;
        Integer clearStatus = beforeQuery(sourceDTO, SqlQueryDTO.builder().build(), false);
        try {
            result = checkViaGrants(hiveSourceDTO.getConnection()) || checkViaTestQuery(hiveSourceDTO.getConnection());
        } catch (Exception e) {
            // 异常为无权限
            result = false;
        } finally {
            DBUtil.closeDBResources(null, null, DBUtil.clearAfterGetConnection(hiveSourceDTO, clearStatus));
        }
        return result;
    }

    /**
     * 通过SHOW GRANTS检查权限
     */
    private boolean checkViaGrants(Connection conn) throws SQLException {
        try (Statement stmt = conn.createStatement(); ResultSet rs = stmt.executeQuery(SHOW_GRANTS)) {
            // 如果能执行SHOW GRANTS命令，说明有基本权限
            return rs.next();
        } catch (SQLException e) {
            // 如果SHOW GRANTS失败，可能是权限不足或命令不支持
            log.debug("SHOW GRANTS failed: {}", e.getMessage());
            return false;
        }
    }

    /**
     * 通过测试查询检查权限
     */
    private boolean checkViaTestQuery(Connection conn) {
        try (Statement stmt = conn.createStatement();
             ResultSet rs = stmt.executeQuery(GET_METADATA_PRIVILEGES)) {
            // 查询成功 = 有权限
            return rs.next();
        } catch (SQLException e) {
            log.debug("Test metadata query failed: {}", e.getMessage());
            return false; // 查询失败 = 无权限
        }
    }
}
