package com.dtstack.dtcenter.common.loader.hive3;

public class SqlConstants {

    //函数常量
    public static final String FUNCTION_TYPE = "FUNCTION";
    //存储过程
    public static final String PROCEDURE_TYPE = "PROCEDURE";
    //表
    public static final String TABLE_TYPE = "TABLE";
    //视图
    public static final String VIEW_TYPE = "VIEW";


    //关联
    public static final String UNION_ALL = " %s UNION ALL %s";

    // hive获取指定database下的表 - 使用SHOW TABLES命令
    public static final String SHOW_TABLE_BY_SCHEMA_SQL_NEW = "SHOW TABLES IN %s";

    // hive获取指定database下的表（带模糊查询）
    public static final String SHOW_TABLE_BY_SCHEMA_LIKE_SQL_NEW = "SHOW TABLES IN %s LIKE '%s'";

    // hive获取指定database下的视图 - Hive 3.x支持SHOW VIEWS
    public static final String SHOW_VIEW_BY_SCHEMA_SQL_NEW = "SHOW VIEWS IN %s";

    // hive获取指定database下的视图（带模糊查询）
    public static final String SHOW_VIEW_BY_SCHEMA_LIKE_SQL_NEW = "SHOW VIEWS IN %s LIKE '%s'";

    // hive3已移除索引功能，这些常量保留但不会被使用
    public static final String SHOW_INDEX_BY_SCHEMA_SQL_NEW = "";

    public static final String SHOW_INDEX_COLUMN_BY_SCHEMA_SQL_NEW = "";

    // hive获取函数列表
    public static final String SHOW_FUNCTION_BY_SCHEMA_SQL_NEW = "SHOW FUNCTIONS";

    // hive获取函数列表（带模糊查询）
    public static final String SHOW_FUNCTION_LIKE_SQL_NEW = "SHOW FUNCTIONS LIKE '%s'";

    // hive获取函数详细信息（包含参数）
    public static final String SHOW_FUNCTION_ARGUMENTS_BY_SCHEMA_SQL_NEW = "DESCRIBE FUNCTION EXTENDED %s";

    // 表存在性检查SQL
    public static final String TABLE_EXISTS_SQL = "SHOW TABLES IN %s LIKE '%s'";

    // 获取表详细信息以判断是否为视图
    public static final String DESCRIBE_FORMATTED_SQL = "DESCRIBE FORMATTED %s";

    //表名模糊搜索sql - 用于SHOW TABLES LIKE命令
    public static final String SEARCH_SQL = "*%s*";

    /**
     * 索引名模糊搜索sql - Hive 3.x已移除索引功能
     */
    public static final String SEARCH_INDEX_SQL = "*%s*";

    /**
     * 函数名模糊搜索sql - 用于SHOW FUNCTIONS LIKE命令
     */
    public static final String SEARCH_FUNCTION_SQL = "*%s*";

}