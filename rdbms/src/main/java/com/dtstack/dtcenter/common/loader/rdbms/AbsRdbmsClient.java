/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.dtstack.dtcenter.common.loader.rdbms;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.dsg.database.datasource.vo.DbTableVO;
import com.dsg.database.datasource.dto.*;
import com.dsg.database.datasource.enums.DataSourceTypeEnum;
import com.dsg.database.datasource.utils.ParseDatasourceUtils;
import com.dtstack.dtcenter.common.loader.common.DtClassConsistent;
import com.dtstack.dtcenter.common.loader.common.DtClassThreadFactory;
import com.dtstack.dtcenter.common.loader.common.exception.ErrorCode;
import com.dtstack.dtcenter.common.loader.common.utils.*;
import com.dtstack.dtcenter.loader.IDownloader;
import com.dtstack.dtcenter.loader.cache.connection.CacheConnectionHelper;
import com.dtstack.dtcenter.loader.client.IClient;
import com.dtstack.dtcenter.loader.dto.*;
import com.dtstack.dtcenter.loader.dto.source.*;
import com.dtstack.dtcenter.loader.enums.CommonDBDataType;
import com.dtstack.dtcenter.loader.enums.ConnectionClearStatus;
import com.dtstack.dtcenter.loader.enums.MatchType;
import com.dtstack.dtcenter.loader.exception.DtLoaderException;
import com.dtstack.dtcenter.loader.source.DataSourceType;
import com.dtstack.dtcenter.loader.utils.AssertUtils;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;

import java.sql.*;
import java.util.*;
import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

/**
 * @company: www.dtstack.com
 * <AUTHOR>
 * @Date ：Created in 15:59 2020/1/3
 * @Description：客户端
 */
@Slf4j
public abstract class AbsRdbmsClient<T> implements IClient<T> {
    private ConnFactory connFactory = getConnFactory();

    /**
     * 获取连接工厂
     *
     * @return
     */
    protected abstract ConnFactory getConnFactory();

    /**
     * 获取数据源类型
     *
     * @return
     */
    protected abstract DataSourceType getSourceType();

    private static final String DONT_EXIST = "doesn't exist";

    private static final String SHOW_DB_SQL = "show databases";

    private static final String TABLE_IS_IN_SCHEMA = "select table_name from information_schema.tables where table_schema='%s' and table_name = '%s'";

    private static final String COUNT_TABLE = "select count(1) from %s";

    private static final Map<Short, String> indexTypeMap = new HashMap<Short, String>() {
        {
            put((short) 0, "tableIndexStatistic");
            put((short) 1, "tableIndexClustered");
            put((short) 2, "tableIndexHashed");
            put((short) 3, "tableIndexOther");
        }
    };

    //线程池 - 用于部分数据源测试连通性超时处理
    protected static ExecutorService executor = new ThreadPoolExecutor(5, 10, 1L, TimeUnit.MINUTES, new ArrayBlockingQueue<>(5), new DtClassThreadFactory("testConnFactory"));

    /**
     * rdbms数据库获取连接唯一入口，对抛出异常进行统一处理
     *
     * @param iSource
     * @return
     * @throws Exception
     */
    @Override
    public Connection getCon(ISourceDTO iSource) {
        return getCon(iSource, null);
    }

    @Override
    public Connection getCon(ISourceDTO iSource, String taskParams) {
        log.info("-------getting connection....-----");
        if (!CacheConnectionHelper.isStart()) {
            try {
                return connFactory.getConn(iSource, taskParams);
            } catch (Exception e) {
                throw new DtLoaderException(e.getMessage(), e);
            }
        }

        return CacheConnectionHelper.getConnection(getSourceType().getVal(), con -> {
            try {
                return connFactory.getConn(iSource, taskParams);
            } catch (Exception e) {
                throw new DtLoaderException(e.getMessage(), e);
            }
        });
    }

    @Override
    public ConnectorResultEntity getResultSetStream(ISourceDTO source, SqlQueryDTO queryDTO, boolean enableStream, boolean autoCommit) {
        /**
         * 开启流式处理需要设置这三个参数
         *
         * ResultSet.TYPE_FORWARD_ONLY 结果集的游标只能向下滚动
         * ResultSet.CONCUR_READ_ONLY 不能用结果集更新数据库中的表
         * Integer.MIN_VALUE
         */
        try {
            Connection conn = getCon(source);
            //设置是否自动提交事务
            conn.setAutoCommit(autoCommit);
            String sqlCmd = queryDTO.getSql();
            log.info("-------sqlCmd: {}", sqlCmd);
            log.info("-------getting resultSet....-----");

            PreparedStatement stmt;
            if (enableStream) {
                stmt = conn.prepareStatement(sqlCmd, ResultSet.TYPE_FORWARD_ONLY, ResultSet.CONCUR_READ_ONLY);
            } else {
                stmt = conn.prepareStatement(sqlCmd);
            }
            if (DataSourceTypeEnum.MySQL.getVal().equals(source.getSourceType())) {
                stmt.setFetchSize(Integer.MIN_VALUE);
            }

            return ConnectorResultEntity.builder()
                    .connection(conn)
                    .preparedStatement(stmt)
                    .resultSet(stmt.executeQuery())
                    .build();
        } catch (Exception e) {
            throw new DtLoaderException(e.getMessage(), e);
        }
    }

    @Override
    public Boolean testCon(ISourceDTO iSource) {
        return connFactory.testConn(iSource);
    }

    /**
     * 执行查询
     *
     * @param rdbmsSourceDTO
     * @param queryDTO
     * @param clearStatus
     * @return
     * @throws SQLException
     */
    public List<Map<String, Object>> executeQuery(RdbmsSourceDTO rdbmsSourceDTO, SqlQueryDTO queryDTO, Integer clearStatus) {
        try {
            Boolean setMaxRow = ReflectUtil.fieldExists(SqlQueryDTO.class, "setMaxRow") ? queryDTO.getSetMaxRow() : null;
            // 预编译字段
            if (queryDTO.getPreFields() != null) {
                return DBUtil.executeQuery(rdbmsSourceDTO.getConnection(), queryDTO.getSql(), queryDTO.getLimit(), queryDTO.getPreFields(), queryDTO.getQueryTimeout(), setMaxRow, this::dealResult);
            }
            return DBUtil.executeQuery(rdbmsSourceDTO.getConnection(), queryDTO.getSql(), queryDTO.getLimit(), queryDTO.getQueryTimeout(), setMaxRow, this::dealResult, queryDTO.getPreSql());
        }catch (Exception e){
            throw new DtLoaderException(String.format("SQL executed exception, %s", e.getMessage()), e);
        } finally {
            DBUtil.closeDBResources(null, null, DBUtil.clearAfterGetConnection(rdbmsSourceDTO, clearStatus));
        }
    }

    @Override
    public List<Map<String, Object>> executeQuery(ISourceDTO iSource, SqlQueryDTO queryDTO) {
        Integer clearStatus = beforeQuery(iSource, queryDTO, true);
        RdbmsSourceDTO rdbmsSourceDTO = (RdbmsSourceDTO) iSource;
        return executeQuery(rdbmsSourceDTO, queryDTO, clearStatus);
    }

    @Override
    public Boolean executeSqlWithoutResultSet(ISourceDTO iSource, SqlQueryDTO queryDTO) {
        Integer clearStatus = beforeQuery(iSource, queryDTO, true);
        RdbmsSourceDTO rdbmsSourceDTO = (RdbmsSourceDTO) iSource;
        try {
            DBUtil.executeSqlWithoutResultSet(rdbmsSourceDTO.getConnection(), queryDTO.getSql());
        } finally {
            DBUtil.closeDBResources(null, null, DBUtil.clearAfterGetConnection(rdbmsSourceDTO, clearStatus));
        }
        return true;
    }


    @Override
    public List<JdbcSqlMetadataInfoDTO> executeSqlForMetadataInfo(ISourceDTO iSource, SqlQueryDTO queryDTO) {
        List<JdbcSqlMetadataInfoDTO> jdbcSqlMetadataInfoDTOS = Lists.newArrayList();
        Integer clearStatus = beforeQuery(iSource, queryDTO, true);
        RdbmsSourceDTO rdbmsSourceDTO = (RdbmsSourceDTO) iSource;
        try {
            jdbcSqlMetadataInfoDTOS = DBUtil.executeSqlForMetadataInfo(rdbmsSourceDTO.getConnection(), queryDTO.getSql());
        } finally {
            DBUtil.closeDBResources(null, null, DBUtil.clearAfterGetConnection(rdbmsSourceDTO, clearStatus));
        }
        return jdbcSqlMetadataInfoDTOS;
    }


    /**
     * 执行查询前的操作
     *
     * @param iSource
     * @param queryDTO
     * @return 是否需要自动关闭连接
     * @throws Exception
     */
    protected Integer beforeQuery(ISourceDTO iSource, SqlQueryDTO queryDTO, boolean query) {
        // 查询 SQL 不能为空
        if (query && StringUtils.isBlank(queryDTO.getSql())) {
            throw new DtLoaderException("Query SQL cannot be empty");
        }

        RdbmsSourceDTO rdbmsSourceDTO = (RdbmsSourceDTO) iSource;
        // 设置 connection
        if (rdbmsSourceDTO.getConnection() == null) {
            rdbmsSourceDTO.setConnection(getCon(iSource));
            if (CacheConnectionHelper.isStart()) {
                return ConnectionClearStatus.NORMAL.getValue();
            }
            return ConnectionClearStatus.CLOSE.getValue();
        }
        return ConnectionClearStatus.NORMAL.getValue();
    }

    /**
     * 执行字段处理前的操作
     *
     * @param iSource
     * @param queryDTO
     * @return
     * @throws Exception
     */
    protected Integer beforeColumnQuery(ISourceDTO iSource, SqlQueryDTO queryDTO) {
        // 查询表不能为空
        Integer clearStatus = beforeQuery(iSource, queryDTO, false);
        if (queryDTO == null || StringUtils.isBlank(queryDTO.getTableName())) {
            throw new DtLoaderException("Query table name cannot be empty");
        }

        queryDTO.setColumns(CollectionUtils.isEmpty(queryDTO.getColumns()) ? Collections.singletonList("*") :
                queryDTO.getColumns());
        return clearStatus;
    }

    @Override
    public List<String> getTableList(ISourceDTO iSource, SqlQueryDTO queryDTO) {
        Integer clearStatus = beforeQuery(iSource, queryDTO, false);
        RdbmsSourceDTO rdbmsSourceDTO = (RdbmsSourceDTO) iSource;
        ResultSet rs = null;
        List<String> tableList = new ArrayList<>();
        try {
            DatabaseMetaData meta = rdbmsSourceDTO.getConnection().getMetaData();
            if (null == queryDTO) {
                rs = meta.getTables(null, null, null, null);
            } else {
                rs = meta.getTables(null, rdbmsSourceDTO.getSchema(), null, DBUtil.getTableTypes(queryDTO));
            }
            while (rs.next()) {
                tableList.add(rs.getString(3));
            }
        } catch (Exception e) {
            throw new DtLoaderException(String.format("Get database table exception：%s", e.getMessage()), e);
        } finally {
            DBUtil.closeDBResources(rs, null, DBUtil.clearAfterGetConnection(rdbmsSourceDTO, clearStatus));
        }
        return SearchUtil.handleSearchAndLimit(tableList, queryDTO);
    }

    @Override
    public List<TableViewDTO> getTableAndViewList(ISourceDTO iSource, SqlQueryDTO queryDTO) {
        Integer clearStatus = beforeQuery(iSource, queryDTO, false);
        RdbmsSourceDTO rdbmsSourceDTO = (RdbmsSourceDTO) iSource;
        ResultSet rs = null;
        List<TableViewDTO> tableList = new ArrayList<>();
        try {
            DatabaseMetaData meta = rdbmsSourceDTO.getConnection().getMetaData();
            if (null == queryDTO) {
                rs = meta.getTables(null, null, null, new String[]{"TABLE", "VIEW"});
            } else {
                if (BooleanUtils.isTrue(queryDTO.getView())) {
                    rs = meta.getTables(null, queryDTO.getSchema(), null, new String[]{"TABLE", "VIEW"});
                } else {
                    rs = meta.getTables(null, queryDTO.getSchema(), null, new String[]{"TABLE"});
                }
            }
            while (rs.next()) {
                TableViewDTO tableViewDTO = new TableViewDTO(rs.getString(3), rs.getString(4));
                tableList.add(tableViewDTO);
            }
        } catch (Exception e) {
            throw new DtLoaderException(String.format("Get database tableView exception：%s", e.getMessage()), e);
        } finally {
            DBUtil.closeDBResources(rs, null, DBUtil.clearAfterGetConnection(rdbmsSourceDTO, clearStatus));
        }
        return SearchUtil.handleSearchAndLimitForTableAndView(tableList, queryDTO);
    }

    /**
     * 父类默认count查询 子类查system表实现
     *
     * @param iSource
     * @param queryDTO
     * @return
     */
    @Override
    public Long getTableRows(ISourceDTO iSource, SqlQueryDTO queryDTO) {
        Integer clearStatus = beforeQuery(iSource, queryDTO, false);
        RdbmsSourceDTO rdbmsSourceDTO = (RdbmsSourceDTO) iSource;
        Statement statement = null;
        ResultSet rs = null;
        long count = 0L;
        try {
            statement = rdbmsSourceDTO.getConnection().createStatement();
            rs = statement.executeQuery(String.format(COUNT_TABLE, queryDTO.getTableName()));
            while (rs.next()) {
                count = rs.getInt(1);
                return count;
            }
        } catch (Exception e) {
            throw new DtLoaderException(String.format("Get table count exception：%s", e.getMessage()), e);
        } finally {
            DBUtil.closeDBResources(rs, statement, DBUtil.clearAfterGetConnection(rdbmsSourceDTO, clearStatus));
        }
        return count;
    }

    @Override
    public String getCharacterSet(ISourceDTO source, SqlQueryDTO queryDTO) {
        throw new DtLoaderException(ErrorCode.NOT_SUPPORT.getDesc());
    }

    @Override
    public String getCharacterCollation(ISourceDTO source, SqlQueryDTO queryDTO) {
        throw new DtLoaderException(ErrorCode.NOT_SUPPORT.getDesc());
    }

    /**
     * 暂不支持太多数据源
     *
     * @param source
     * @param queryDTO
     * @return
     * @throws Exception
     */
    @Override
    public List<String> getTableListBySchema(ISourceDTO source, SqlQueryDTO queryDTO) {
        String sql = getTableBySchemaSql(source, queryDTO);
        Integer fetchSize = ReflectUtil.fieldExists(SqlQueryDTO.class, "fetchSize") ? queryDTO.getFetchSize() : null;
        return queryWithSingleColumn(source, fetchSize, sql, 1, "get table exception according to schema...");
    }

    /**
     * 执行查询sql，结果为单列
     *
     * @param source      数据源信息
     * @param fetchSize   fetchSize
     * @param sql         sql信息
     * @param columnIndex 取第几列
     * @param errMsg      错误信息
     * @return 查询结果
     */
    protected List<String> queryWithSingleColumn(ISourceDTO source, Integer fetchSize, String sql, Integer columnIndex, String errMsg) {
        return queryWithSingleColumn(source, fetchSize, sql, columnIndex, errMsg, null);
    }

    /**
     * 执行查询sql，结果为单列
     *
     * @param source      数据源信息
     * @param fetchSize   fetchSize
     * @param sql         sql信息
     * @param columnIndex 取第几列
     * @param errMsg      错误信息
     * @param limit       条数限制
     * @return 查询结果
     */
    protected List<String> queryWithSingleColumn(ISourceDTO source, Integer fetchSize, String sql, Integer columnIndex, String errMsg, Integer limit) {
        Integer clearStatus = beforeQuery(source, SqlQueryDTO.builder().sql(sql).build(), true);
        RdbmsSourceDTO rdbmsSourceDTO = (RdbmsSourceDTO) source;
        log.info("The SQL executed by method queryWithSingleColumn is:{}", sql);
        Statement statement = null;
        ResultSet rs = null;
        List<String> result = new ArrayList<>();
        try {
            statement = rdbmsSourceDTO.getConnection().createStatement();
            DBUtil.setFetchSize(statement, fetchSize);
            if (Objects.nonNull(limit)) {
                statement.setMaxRows(limit);
            }
            rs = statement.executeQuery(sql);
            while (rs.next()) {
                result.add(rs.getString(columnIndex == null ? 1 : columnIndex));
            }
        } catch (Exception e) {
            throw new DtLoaderException(String.format("%s:%s", errMsg, e.getMessage()), e);
        } finally {
            DBUtil.closeDBResources(rs, statement, DBUtil.clearAfterGetConnection(rdbmsSourceDTO, clearStatus));
        }
        return result;
    }

    /**
     * 执行查询sql，结果为自定义列
     *
     * @param source    数据源信息
     * @param fetchSize fetchSize
     * @param sql       sql信息
     * @param errMsg    错误信息
     * @return 查询结果
     */
    protected List<Map<String, Object>> queryCustomColumn(ISourceDTO source, Integer fetchSize, String sql, String errMsg) {
        return queryCustomColumn(source, fetchSize, sql, errMsg, null);
    }

    /**
     * 执行查询sql，结果为自定义列
     *
     * @param source    数据源信息
     * @param fetchSize fetchSize
     * @param sql       sql信息
     * @param errMsg    错误信息
     * @param limit     条数限制
     * @return 查询结果
     */
    protected List<Map<String, Object>> queryCustomColumn(ISourceDTO source, Integer fetchSize, String sql, String errMsg, Integer limit) {
        Integer clearStatus = beforeQuery(source, SqlQueryDTO.builder().sql(sql).build(), true);
        RdbmsSourceDTO rdbmsSourceDTO = (RdbmsSourceDTO) source;
        log.info("The SQL executed by method queryWithSingleColumn is:{}", sql);
        Statement statement = null;
        ResultSet rs = null;
        List<Map<String, Object>> result = new ArrayList<>();
        try {
            statement = rdbmsSourceDTO.getConnection().createStatement();
            DBUtil.setFetchSize(statement, fetchSize);
            if (Objects.nonNull(limit)) {
                statement.setMaxRows(limit);
            }
            rs = statement.executeQuery(sql);
            ResultSetMetaData metaData = rs.getMetaData();
            int columnCount = metaData.getColumnCount();
            while (rs.next()) {
                Map<String, Object> temp = new HashMap<>();
                for (int i = 1; i <= columnCount; i++) {
                    String columnName = metaData.getColumnName(i);
                    Object object = rs.getObject(i);
                    temp.put(columnName, object);
                }
                result.add(temp);
            }
        } catch (Exception e) {
            throw new DtLoaderException(String.format("%s:%s", errMsg, e.getMessage()), e);
        } finally {
            DBUtil.closeDBResources(rs, statement, DBUtil.clearAfterGetConnection(rdbmsSourceDTO, clearStatus));
        }
        return result;
    }

    @Override
    public List<String> getColumnClassInfo(ISourceDTO iSource, SqlQueryDTO queryDTO) {
        Integer clearStatus = beforeColumnQuery(iSource, queryDTO);
        RdbmsSourceDTO rdbmsSourceDTO = (RdbmsSourceDTO) iSource;

        Statement stmt = null;
        ResultSet rs = null;
        try {
            stmt = rdbmsSourceDTO.getConnection().createStatement();
            String queryColumnSql =
                    "select " + CollectionUtil.listToStr(queryDTO.getColumns()) + " from " + transferSchemaAndTableName(rdbmsSourceDTO, queryDTO)
                            + " where 1=2";
            rs = stmt.executeQuery(queryColumnSql);
            ResultSetMetaData rsmd = rs.getMetaData();
            int cnt = rsmd.getColumnCount();
            List<String> columnClassNameList = Lists.newArrayList();

            for (int i = 0; i < cnt; i++) {
                String columnClassName = rsmd.getColumnClassName(i + 1);
                columnClassNameList.add(columnClassName);
            }

            return columnClassNameList;
        } catch (Exception e) {
            throw new DtLoaderException(e.getMessage(), e);
        } finally {
            DBUtil.closeDBResources(rs, stmt, DBUtil.clearAfterGetConnection(rdbmsSourceDTO, clearStatus));
        }
    }

    @Override
    public List<ColumnMetaDTO> getColumnMetaDataWithSql(ISourceDTO iSource, SqlQueryDTO queryDTO) {
        Integer clearStatus = beforeQuery(iSource, queryDTO, true);
        RdbmsSourceDTO rdbmsSourceDTO = (RdbmsSourceDTO) iSource;
        Statement statement = null;
        ResultSet rs = null;
        List<ColumnMetaDTO> columns = new ArrayList<>();
        try {
            statement = rdbmsSourceDTO.getConnection().createStatement();
            statement.setMaxRows(1);
            String queryColumnSql = queryDTO.getSql();
            rs = statement.executeQuery(queryColumnSql);
            ResultSetMetaData rsMetaData = rs.getMetaData();
            for (int i = 0, len = rsMetaData.getColumnCount(); i < len; i++) {
                ColumnMetaDTO columnMetaDTO = new ColumnMetaDTO();
                columnMetaDTO.setKey(rsMetaData.getColumnLabel(i + 1));
                columnMetaDTO.setType(doDealType(rsMetaData, i));
                columnMetaDTO.setPart(false);
                // 获取字段精度
                if (columnMetaDTO.getType().equalsIgnoreCase("decimal")
                        || columnMetaDTO.getType().equalsIgnoreCase("float")
                        || columnMetaDTO.getType().equalsIgnoreCase("double")
                        || columnMetaDTO.getType().equalsIgnoreCase("numeric")) {
                    columnMetaDTO.setScale(rsMetaData.getScale(i + 1));
                    columnMetaDTO.setPrecision(rsMetaData.getPrecision(i + 1));
                }

                columns.add(columnMetaDTO);
            }
            return columns;

        } catch (SQLException e) {
            if (e.getMessage().contains(DONT_EXIST)) {
                throw new DtLoaderException(String.format(queryDTO.getTableName() + "table not exist,%s", e.getMessage()), e);
            } else {
                throw new DtLoaderException(String.format("Failed to get the meta information of the fields of the table: %s. Please contact the DBA to check the database and table information: %s",
                        queryDTO.getTableName(), e.getMessage()), e);
            }
        } finally {
            DBUtil.closeDBResources(rs, statement, DBUtil.clearAfterGetConnection(rdbmsSourceDTO, clearStatus));
        }
    }

    @Override
    public List<ColumnMetaDTO> getColumnMetaData(ISourceDTO iSource, SqlQueryDTO queryDTO) {
        Integer clearStatus = beforeColumnQuery(iSource, queryDTO);
        RdbmsSourceDTO rdbmsSourceDTO = (RdbmsSourceDTO) iSource;
        Set<ColumnMetaDTO> columns = new HashSet<>();
        List<ColumnMetaDTO> newColumns = new ArrayList<>();
        Statement statement = null;
        ResultSet rs = null;
        //修改 dgr 20220722
        ResultSet rsColumn = null;
        ResultSet pkRs = null;
        ResultSet fkRs = null;
        ResultSet uniqueRs = null;
        ResultSet allIndexRs = null;
        try {
            log.info("------------------开始getColumnMetaData------------------");
            log.error("dsx采集表：{}",queryDTO.getTableName());
            rdbmsSourceDTO.setConnection(getTransConn(rdbmsSourceDTO.getConnection()));
            DatabaseMetaData metaData = rdbmsSourceDTO.getConnection().getMetaData();
            log.info("------------------start------------------");

            String catalog = rdbmsSourceDTO.getConnection().getCatalog();
            if(StringUtils.isNotEmpty(catalog)&&StringUtils.isNotEmpty(queryDTO.getSchema())){
                catalog=queryDTO.getSchema();
            }
            ArrayList<String> pkList = new ArrayList<>();
            try {
                pkRs = metaData.getPrimaryKeys(catalog, queryDTO.getSchema(), queryDTO.getTableName());
                while (pkRs.next()) {
                    pkList.add(pkRs.getString("COLUMN_NAME"));
                }
            }catch (Exception e){
                log.error("getColumnMetaData pkRs error:{}",e.getMessage());
            }


            log.info("------------------执行pkRs结束------------------");
            ArrayList<String> fkList = new ArrayList<>();
            try{
                fkRs = metaData.getExportedKeys(catalog, queryDTO.getSchema(), queryDTO.getTableName());
                while (fkRs.next()) {
                    fkList.add(fkRs.getString("PKCOLUMN_NAME"));
                }
            }catch (Exception e){
                log.error("getColumnMetaData fkRs error:{}",e.getMessage());
            }


            log.info("------------------执行fkRs结束------------------");

            //oracle视图和oracle表名为小写的时候不支持查询索引
            ArrayList<String> uniqueList = new ArrayList<>();
            ArrayList<DsIndexDTO> allIndexList = new ArrayList<>();
            /*if ((!(rdbmsSourceDTO instanceof OracleSourceDTO) ||
                    (Arrays.stream(queryDTO.getTableTypes()).noneMatch("VIEW"::equalsIgnoreCase))
                            && !queryDTO.getTableName().matches(".*[a-z].*"))) {
                log.info("------------------单独执行uniqueRs  start ------------------");
                uniqueRs = metaData.getIndexInfo(rdbmsSourceDTO.getConnection().getCatalog(), rdbmsSourceDTO.getSchema(), queryDTO.getTableName(), true, false);
                uniqueRs.getStatement().setMaxRows(1);
                log.info("------------------单独执行uniqueRs  end ------------------");
                while (uniqueRs.next()) {
                    uniqueList.add(uniqueRs.getString("COLUMN_NAME"));
                }
                allIndexRs = metaData.getIndexInfo(rdbmsSourceDTO.getConnection().getCatalog(), rdbmsSourceDTO.getSchema(), queryDTO.getTableName(), false, false);
                log.info("------------------单独执行allIndexRs------------------");
                while (allIndexRs.next()) {
                    DsIndexDTO dsIndexDTO = new DsIndexDTO();
                    dsIndexDTO.setColumnName(allIndexRs.getString("COLUMN_NAME"));
                    dsIndexDTO.setUnique(allIndexRs.getBoolean("NON_UNIQUE"));
                    dsIndexDTO.setType(allIndexRs.getShort("TYPE"));
                    allIndexList.add(dsIndexDTO);
                }
            }

            log.info("------------------执行oracle视图和oracle表名为小写的时候不支持查询索引------------------");*/

            rsColumn = metaData.getColumns(catalog, queryDTO.getSchema(), queryDTO.getTableName(), null);
            while (rsColumn.next()) {
                ColumnMetaDTO columnMetaDTO = new ColumnMetaDTO();
                columnMetaDTO.setPart(false);
                columnMetaDTO.setKey(rsColumn.getString("COLUMN_NAME"));
                columnMetaDTO.setType(rsColumn.getString("TYPE_NAME"));
                columnMetaDTO.setComment(rsColumn.getString("REMARKS"));
                columnMetaDTO.setScale(rsColumn.getInt("DECIMAL_DIGITS"));
                columnMetaDTO.setLength(rsColumn.getInt("COLUMN_SIZE"));
                columnMetaDTO.setDataType(rsColumn.getInt("DATA_TYPE"));
                columnMetaDTO.setDefaultValue(rsColumn.getString("COLUMN_DEF"));
                columnMetaDTO.setNotNullFlag("no".equals(rsColumn.getString("IS_NULLABLE").toLowerCase()));
                if (pkList.contains(rsColumn.getString("COLUMN_NAME"))) {
                    columnMetaDTO.setPkflag(true);
                } else {
                    columnMetaDTO.setPkflag(false);
                }
                if (fkList.contains(rsColumn.getString("COLUMN_NAME"))) {
                    columnMetaDTO.setFkflag(true);
                } else {
                    columnMetaDTO.setFkflag(false);
                }
                if (uniqueList.contains(rsColumn.getString("COLUMN_NAME"))) {
                    columnMetaDTO.setUniqueFlag(true);
                } else {
                    columnMetaDTO.setUniqueFlag(false);
                }

                for (DsIndexDTO dsIndexDTO : allIndexList
                ) {
                    if (rsColumn.getString("COLUMN_NAME").equals(dsIndexDTO.getColumnName())) {
                        columnMetaDTO.setIndexType(indexTypeMap.get(dsIndexDTO.getType()));
                    }
                }

                columns.add(columnMetaDTO);
            }

            log.info("------------------执行getColumns结束------------------");

            statement = rdbmsSourceDTO.getConnection().createStatement();
            statement.setMaxRows(1);
            String queryColumnSql =
                    "select " + CollectionUtil.listToStr(queryDTO.getColumns()) + " from " + transferSchemaAndTableName(rdbmsSourceDTO, queryDTO) + " where 1=2";

            log.info("------------------queryColumnSql{}------------------",queryColumnSql);
            // 忽略大小写
            //todo  数据源 获取字段  表名和schema  拼接符 待处理 即：集成 预览数据 忽略表名大小写
//            if (BooleanUtils.isTrue(queryDTO.getIsCapitalization())) {
//                queryColumnSql = queryColumnSql.replaceAll("\"", "");
//                queryColumnSql = queryColumnSql.replaceAll("`", "");
//                queryColumnSql = queryColumnSql.replaceAll("\\[", "");
//                queryColumnSql = queryColumnSql.replaceAll("]", "");
//            }
            rs = statement.executeQuery(queryColumnSql);


            log.info("------------------执行select结束------------------");

            ResultSetMetaData rsMetaData = rs.getMetaData();
            int columnCount = rsMetaData.getColumnCount();
            for (int i = 1; i <= columnCount; i++) {
                String columnName = rsMetaData.getColumnName(i);
                for (ColumnMetaDTO columnMetaDTO : columns) {
                    if (columnMetaDTO.getKey().equals(columnName)) {
                        columnMetaDTO.setPrecision(rsMetaData.getPrecision(i));
                        columnMetaDTO.setDateType(rsMetaData.getColumnClassName(i));
                        newColumns.add(columnMetaDTO);
                    }
                }
            }

        } catch (SQLException e) {
            log.error("------------------表{}，字段采集报错{}------------------",queryDTO.getTableName(),e.getMessage());
            if (e.getMessage().contains(DONT_EXIST)) {
                throw new DtLoaderException(String.format(queryDTO.getTableName() + "table not exist,%s", e.getMessage()), e);
            } else {
                throw new DtLoaderException(String.format("Failed to get the meta information of the fields of the table: %s. Please contact the DBA to check the database and table information: %s",
                        queryDTO.getTableName(), e.getMessage()), e);
            }
        } finally {
            DBUtil.closeDBResources(pkRs, null, null);
            DBUtil.closeDBResources(fkRs, null, null);
            DBUtil.closeDBResources(uniqueRs, null, null);
            DBUtil.closeDBResources(allIndexRs, null, null);
            DBUtil.closeDBResources(rsColumn, null, DBUtil.clearAfterGetConnection(rdbmsSourceDTO, clearStatus));
            DBUtil.closeDBResources(rs, statement, DBUtil.clearAfterGetConnection(rdbmsSourceDTO, clearStatus));
        }
        return newColumns;
    }

    public Connection getTransConn(Connection conn) throws SQLException {
        return conn;
    }

    @Override
    public List<ColumnMetaDTO> getFlinkColumnMetaData(ISourceDTO iSource, SqlQueryDTO queryDTO) {
        return getColumnMetaData(iSource, queryDTO);
    }

    @Override
    public String getTableMetaComment(ISourceDTO iSource, SqlQueryDTO queryDTO) {
        return "";
    }

    /**
     * rdbms数据预览
     *
     * @param iSource
     * @param queryDTO
     * @return
     * @throws Exception
     */
    @Override
    public List<List<Object>> getPreview(ISourceDTO iSource, SqlQueryDTO queryDTO) {
        Integer clearStatus = beforeColumnQuery(iSource, queryDTO);
        RdbmsSourceDTO rdbmsSourceDTO = (RdbmsSourceDTO) iSource;
        List<List<Object>> previewList = new ArrayList<>();
        if (StringUtils.isBlank(queryDTO.getTableName())) {
            return previewList;
        }
        Statement stmt = null;
        ResultSet rs = null;
        PreparedStatement preparedStatement = null;
        try {
            if ((iSource instanceof ImpalaSourceDTO
                    || iSource instanceof Hive1SourceDTO
                    || iSource instanceof HiveSourceDTO
                    || iSource instanceof Hive3SourceDTO)
                    && queryDTO.getPageNum() != null) {
                String sql = String.format("SELECT * from %s ", transferSchemaAndTableName(iSource, queryDTO));
                String whereSql = dealWhereSql(rdbmsSourceDTO, sql, queryDTO);
                if (iSource instanceof Hive1SourceDTO
                        || iSource instanceof HiveSourceDTO
                        || iSource instanceof Hive3SourceDTO) {
                    sql = sql + whereSql + " limit ? offset ?";
                } else {
                    String impalaOrderByColumn = queryDTO.getImpalaOrderByColumn();
                    if (StringUtils.isEmpty(impalaOrderByColumn)) {
                        throw new DtLoaderException("Impala数据源预览分页数据需要order by的字段");
                    }
                    sql = sql + whereSql + String.format(" order by `%s` limit ? offset ?", impalaOrderByColumn);
                }

                preparedStatement = rdbmsSourceDTO.getConnection().prepareStatement(sql);
                Integer previewNum = queryDTO.getPreviewNum();
                Integer pageNum = queryDTO.getPageNum();
                preparedStatement.setInt(1, previewNum);
                preparedStatement.setInt(2, (pageNum - 1) * previewNum);
                rs = preparedStatement.executeQuery();
            } else {
                if (iSource.getSourceType().equals(DataSourceType.Informix.getVal())) {
                    //informix表字段类型为text时报错 Scroll cursor can't select blob columns.
                    stmt = rdbmsSourceDTO.getConnection().createStatement(ResultSet.TYPE_FORWARD_ONLY, ResultSet.CONCUR_READ_ONLY);
                } else {
                    stmt = rdbmsSourceDTO.getConnection().createStatement(ResultSet.TYPE_SCROLL_INSENSITIVE, ResultSet.CONCUR_READ_ONLY);
                }
                //查询sql，默认预览100条
                String querySql = dealSql(rdbmsSourceDTO, queryDTO);
                String whereSql = dealWhereSql(rdbmsSourceDTO, querySql, queryDTO);
                querySql = querySql + whereSql;
                if (queryDTO.getPreviewNum() != null) {
                    int endIndex = queryDTO.getPreviewNum();
                    Integer pageNum = queryDTO.getPageNum();
                    if (pageNum != null) {
                        endIndex = pageNum * endIndex;
                    }
                    stmt.setMaxRows(endIndex);
                }
                rs = stmt.executeQuery(querySql);

                if (queryDTO.getPreviewNum() != null) {
                    int previewNum = queryDTO.getPreviewNum();
                    Integer pageNum = queryDTO.getPageNum();
                    if (pageNum != null) {
                        // 查询分页数据
                        int beginIndex = (pageNum - 1) * previewNum;
                        if (beginIndex > 0) {
                            // 设置读取数据的起始位置
                            rs.absolute(beginIndex);
                        }
                    }
                }
            }

            ResultSetMetaData rsmd = rs.getMetaData();
            //存储字段信息
            List<Object> metaDataList = Lists.newArrayList();
            //字段数量
            int len = rsmd.getColumnCount();
            for (int i = 0; i < len; i++) {
                metaDataList.add(rsmd.getColumnLabel(i + 1));
            }
            previewList.add(metaDataList);
            while (rs.next()) {
                //一个columnData存储一行数据信息
                ArrayList<Object> columnData = Lists.newArrayList();
                for (int i = 0; i < len; i++) {
                    String result = dealPreviewResult(rs.getObject(i + 1));
                    columnData.add(result);
                }
                previewList.add(columnData);
            }
        } catch (Exception e) {
            throw new DtLoaderException(e.getMessage(), e);
        } finally {
            DBUtil.closeDBResources(rs, stmt, DBUtil.clearAfterGetConnection(rdbmsSourceDTO, clearStatus));
            if (preparedStatement != null) {
                try {
                    preparedStatement.close();
                } catch (SQLException e) {
                    e.printStackTrace();
                }
            }
        }
        return previewList;
    }

    @Override
    public int getPreviewRows(ISourceDTO source, SqlQueryDTO queryDTO) {
        Integer clearStatus = beforeQuery(source, queryDTO, false);
        RdbmsSourceDTO rdbmsSourceDTO = (RdbmsSourceDTO) source;
        Statement statement = null;
        ResultSet rs = null;
        int count = 0;
        try {
            String querySql = dealCountPreviewSql(rdbmsSourceDTO, queryDTO);
            String whereSql = dealWhereSql(rdbmsSourceDTO, querySql, queryDTO);
            querySql = querySql + whereSql;
            statement = rdbmsSourceDTO.getConnection().createStatement();
            rs = statement.executeQuery(querySql);
            while (rs.next()) {
                count = rs.getInt(1);
                break;
            }
            if ((source instanceof Hive1SourceDTO
                    || source instanceof HiveSourceDTO
                    || source instanceof Hive3SourceDTO) && count == 0) {
                //hive如果是通过文件方式写入的数据，需要刷新元数据，参考：https://www.cnblogs.com/gxgd/p/15603595.html
                try {
                    statement.executeQuery(String.format("ANALYZE TABLE %s COMPUTE STATISTICS", transferSchemaAndTableName(source, queryDTO)));
                } catch (Exception e) {
                    log.warn("需要刷新元数据异常，设置count为0", e);
                }
                rs = statement.executeQuery(querySql);
                while (rs.next()) {
                    count = rs.getInt(1);
                    break;
                }
            }
        } catch (Exception e) {
            throw new DtLoaderException(String.format("Get preview count exception：%s", e.getMessage()), e);
        } finally {
            DBUtil.closeDBResources(rs, statement, DBUtil.clearAfterGetConnection(rdbmsSourceDTO, clearStatus));
        }
        return count;
    }

    /**
     * 处理 RDBMS 数据源数据预览结果，返回 string 类型
     *
     * @param result 查询结果
     * @return 处理后的结果
     */
    protected String dealPreviewResult(Object result) {
        Object dealResult = dealResult(result);
        // 提前进行 toString
        return Objects.isNull(dealResult) ? null : dealResult.toString();
    }

    /**
     * 处理jdbc查询结果
     *
     * @param result 查询结果
     * @return 处理后的结果
     */
    protected Object dealResult(Object result) {
        return result;
    }

    /**
     * 处理sql语句预览条数
     *
     * @param sqlQueryDTO 查询条件
     * @return 处理后的查询sql
     */
    @Override
    public String dealSql(ISourceDTO sourceDTO, SqlQueryDTO sqlQueryDTO) {
        return "select * from " + transferSchemaAndTableName(sourceDTO, sqlQueryDTO);
    }

    /**
     * 处理sql语句预览条数
     *
     * @param sqlQueryDTO 查询条件
     * @return 处理后的查询sql
     */
    protected String dealCountPreviewSql(ISourceDTO sourceDTO, SqlQueryDTO sqlQueryDTO) {
        return "select count(1) from " + transferSchemaAndTableName(sourceDTO, sqlQueryDTO);
    }

    /**
     * 处理sql语句where条件
     *
     * @param sqlQueryDTO 查询条件
     * @return 处理后的结果
     */
    @Override
    public String dealWhereSql(ISourceDTO source, String querySql, SqlQueryDTO sqlQueryDTO) {
        List<RelationConditionDTO> whereConditions = sqlQueryDTO.getWhereConditions();
        if (CollectionUtils.isEmpty(whereConditions)) {
            return "";
        }

        StringBuilder whereClause = new StringBuilder();
        if (!StringUtils.isEmpty(querySql) && querySql.toLowerCase().contains(" where ")) {
            whereClause.append(" and (");
        } else {
            whereClause.append(" where (");
        }
        int size = whereConditions.size();
        for (int i = 0; i < size; i++) {
            RelationConditionDTO relationConditionDTO = whereConditions.get(i);
            whereClause.append(getDbSeparator())
                    .append(relationConditionDTO.getColumnName())
                    .append(getDbSeparator());

            if (relationConditionDTO.getColumnValue() == null) {
                //处理null值
                whereClause.append(" is null");
            } else {
                if (source instanceof SqlserverSourceDTO
                        || source instanceof Sqlserver2017SourceDTO
                        || source instanceof OracleSourceDTO
                        || source instanceof Greenplum6SourceDTO) {
                    String columnType = relationConditionDTO.getColumnType();
                    if ("NUMBER".equalsIgnoreCase(columnType)) {
                        whereClause.append(" = ").append(relationConditionDTO.getColumnValue());
                    } else {
                        whereClause.append(" = '")
                                .append(relationConditionDTO.getColumnValue())
                                .append("'");
                    }
                } else {
                    whereClause.append(" = \"")
                            .append(relationConditionDTO.getColumnValue())
                            .append("\"");
                }
            }

            if (i < size - 1) {
                whereClause.append(sqlQueryDTO.getWhereRelation())
                        .append(" ");
            } else {
                whereClause.append(")");
            }
        }

        return whereClause.toString();
    }

    /**
     * 获取数据库分隔符
     */
    protected String getDbSeparator() {
        return "`";
    }

    /**
     * 处理 schema 和 表名，优先从 SqlQueryDTO 获取 schema
     *
     * @param sourceDTO   数据源连接信息
     * @param sqlQueryDTO 查询信息
     * @return 处理后的 schema 和 table
     */
    protected String transferSchemaAndTableName(ISourceDTO sourceDTO, SqlQueryDTO sqlQueryDTO) {
        RdbmsSourceDTO rdbmsSourceDTO = (RdbmsSourceDTO) sourceDTO;
        String schema = StringUtils.isNotBlank(sqlQueryDTO.getSchema()) ? sqlQueryDTO.getSchema() : rdbmsSourceDTO.getSchema();
        return transferSchemaAndTableName(schema, sqlQueryDTO.getTableName());
    }

    /**
     * 获取限制条数 sql
     *
     * @param limit 限制条数
     * @return 限制条数 sql
     */
    protected String limitSql(Integer limit) {
        if (Objects.isNull(limit) || limit < 1) {
            throw new DtLoaderException(String.format("limit number [%s] is error", limit));
        }
        return " limit " + limit;
    }

    /**
     * 处理schema和表名
     *
     * @param schema
     * @param tableName
     * @return
     */
    protected String transferSchemaAndTableName(String schema, String tableName) {
        return transferTableName(tableName);
    }

    /**
     * 处理表名
     *
     * @param tableName
     * @return
     */
    @Deprecated
    protected String transferTableName(String tableName) {
        return tableName;
    }

    /**
     * 处理字段类型
     */
    protected String doDealType(ResultSetMetaData rsMetaData, Integer los) throws SQLException {
        return rsMetaData.getColumnTypeName(los + 1);
    }

    protected Map<String, String> getColumnComments(RdbmsSourceDTO sourceDTO, SqlQueryDTO queryDTO) {
        return Collections.emptyMap();
    }

    /**
     * 根据schema获取表，默认不支持。需要支持的数据源自己去实现该方法
     *
     * @param queryDTO 查询queryDTO
     * @return sql语句
     */
    protected String getTableBySchemaSql(ISourceDTO sourceDTO, SqlQueryDTO queryDTO) {
        throw new DtLoaderException(ErrorCode.NOT_SUPPORT.getDesc());
    }

    @Override
    public IDownloader getDownloader(ISourceDTO source, SqlQueryDTO queryDTO) throws Exception {
        throw new DtLoaderException(ErrorCode.NOT_SUPPORT.getDesc());
    }

    @Override
    public IDownloader getDownloader(ISourceDTO source, String sql, Integer pageSize) throws Exception {
        throw new DtLoaderException(ErrorCode.NOT_SUPPORT.getDesc());
    }

    @Override
    public List<String> getAllDatabases(ISourceDTO source, SqlQueryDTO queryDTO) {
        // 获取表信息需要通过show databases 语句
        String sql = getShowDbSql();
        return queryWithSingleColumn(source, null, sql, 1, "get All database Or schema exception");
    }

    @Override
    public List<String> getAllDbs(ISourceDTO source, SqlQueryDTO queryDTO) {
        // 获取表信息需要通过show databases 语句
        String sql = getDbsSql();
        return queryWithSingleColumn(source, null, sql, 1, "get All database exception");
    }

    @Override
    public List<String> getRootDatabases(ISourceDTO source, SqlQueryDTO queryDTO) {
        throw new DtLoaderException(ErrorCode.NOT_SUPPORT.getDesc());
    }

    @Override
    public String getCreateTableSql(ISourceDTO source, SqlQueryDTO queryDTO) {
        Integer clearStatus = beforeQuery(source, queryDTO, false);
        RdbmsSourceDTO rdbmsSourceDTO = (RdbmsSourceDTO) source;

        // 获取表信息需要通过show databases 语句
        String tableName;
        if (StringUtils.isNotEmpty(queryDTO.getSchema())) {
            tableName = queryDTO.getSchema() + "." + queryDTO.getTableName();
        } else {
            tableName = queryDTO.getTableName();
        }
        String sql = queryDTO.getSql() == null ? "show create table " + tableName : queryDTO.getSql();
        String sql1 = queryDTO.getSql();
        if(StringUtils.isEmpty(sql1)){
            if("function".equalsIgnoreCase(queryDTO.getType())){
                sql = "show create function " + tableName;
            }
            if("procedure".equalsIgnoreCase(queryDTO.getType())){
                sql = "show create procedure " + tableName;
            }
        }else{
            sql=sql1;
        }
        Statement statement = null;
        ResultSet rs = null;
        String createTableSql = null;
        try {
            statement = rdbmsSourceDTO.getConnection().createStatement();
            rs = statement.executeQuery(sql);
            int  columnSize = rs.getMetaData().getColumnCount();
            log.error("getCreateTableSql columnSize:{}",columnSize);
            if("function".equalsIgnoreCase(queryDTO.getType()) || "procedure".equalsIgnoreCase(queryDTO.getType())){
                if(columnSize<=2){
                    columnSize = columnSize == 1 ? 1 : 2;
                }else{
                    columnSize=3;
                }
            }else{
                columnSize = columnSize == 1 ? 1 : 2;
            }
            log.error("getCreateTableSql columnSize end:{}",columnSize);
            while (rs.next()) {
                createTableSql = rs.getString(columnSize);
                break;
            }
        } catch (Exception e) {
            throw new DtLoaderException(String.format("failed to get the create table sql：%s", e.getMessage()), e);
        } finally {
            DBUtil.closeDBResources(rs, statement, DBUtil.clearAfterGetConnection(rdbmsSourceDTO, clearStatus));
        }
        return createTableSql;
    }

    @Override
    public List<ColumnMetaDTO> getPartitionColumn(ISourceDTO source, SqlQueryDTO queryDTO) {
        return Collections.emptyList();
    }

    @Override
    public Table getTable(ISourceDTO source, SqlQueryDTO queryDTO) {
        Integer clearStatus = beforeQuery(source, queryDTO, false);
        RdbmsSourceDTO rdbmsSourceDTO = (RdbmsSourceDTO) source;
        Table table = new Table();
        try {
            List<ColumnMetaDTO> columnMetaData = getColumnMetaData(source, queryDTO);
            String tableComment = getTableMetaComment(source, queryDTO);
            table.setColumns(columnMetaData);
            table.setName(queryDTO.getTableName());
            table.setComment(tableComment);
        } catch (Exception e) {
            throw new DtLoaderException(String.format("SQL executed exception: %s", e.getMessage()), e);
        } finally {
            DBUtil.closeDBResources(null, null, DBUtil.clearAfterGetConnection(rdbmsSourceDTO, clearStatus));
        }
        return table;
    }

    /**
     * 获取所有 数据库/schema sql语句
     *
     * @return
     */
    protected String getShowDbSql() {
        return SHOW_DB_SQL;
    }


    /**
     * 获取所有 数据库 sql语句
     *
     * @return
     */
    protected String getDbsSql() {
        return SHOW_DB_SQL;
    }
    @Override
    public String getCurrentSchema(ISourceDTO source) {
        // 获取根据schema获取表的sql
        String sql = getCurrentSchemaSql();
        List<String> result = queryWithSingleColumn(source, null, sql, 1, "failed to get the currently used database");
        if (CollectionUtils.isEmpty(result)) {
            throw new DtLoaderException("failed to get the currently used database");
        }
        return result.get(0);
    }

    protected String getCurrentSchemaSql() {
        throw new DtLoaderException(ErrorCode.NOT_SUPPORT.getDesc());
    }

    @Override
    public String getCurrentDatabase(ISourceDTO source) {
        // 获取根据schema获取表的sql
        String sql = getCurrentDbSql();
        List<String> result = queryWithSingleColumn(source, null, sql, 1, "failed to get the currently used database");
        if (CollectionUtils.isEmpty(result)) {
            throw new DtLoaderException("failed to get the currently used database");
        }
        return result.get(0);
    }

    /**
     * 获取当前使用db的sql语句，需要支持的数据源中重写该方法
     *
     * @return 对应的sql
     */
    protected String getCurrentDbSql() {
        throw new DtLoaderException(ErrorCode.NOT_SUPPORT.getDesc());
    }

    @Override
    public Boolean createDatabase(ISourceDTO source, String dbName, String comment) {
        if (StringUtils.isBlank(dbName)) {
            throw new DtLoaderException("database or schema cannot be empty");
        }
        String createSchemaSql = getCreateDatabaseSql(dbName, comment);
        return executeSqlWithoutResultSet(source, SqlQueryDTO.builder().sql(createSchemaSql).build());
    }

    @Override
    public List<String> getCatalogs(ISourceDTO source) {
        String showCatalogsSql = getCatalogSql();
        return queryWithSingleColumn(source, null, showCatalogsSql, 1, "failed to get data source directory list");
    }

    /**
     * 获取创建库的sql，需要支持的数据源去实现该方法
     *
     * @param dbName  库名
     * @param comment 注释
     * @return sql
     */
    protected String getCreateDatabaseSql(String dbName, String comment) {
        throw new DtLoaderException(ErrorCode.NOT_SUPPORT.getDesc());
    }


    @Override
    public Boolean isDatabaseExists(ISourceDTO source, String dbName) {
        throw new DtLoaderException(ErrorCode.NOT_SUPPORT.getDesc());
    }

    @Override
    public Boolean isTableExistsInDatabase(ISourceDTO source,SqlQueryDTO queryDTO) {
        if (StringUtils.isBlank(queryDTO.getDbName())) {
            throw new DtLoaderException("database name is not empty");
        }
        String tableExistSql = getTableExistSql(source, queryDTO);
        log.info("tableExistSql: {}", tableExistSql);
        return CollectionUtils.isNotEmpty(executeQuery(source, SqlQueryDTO.builder().sql(tableExistSql).build()));

    }
    protected String getTableExistSql(ISourceDTO source,SqlQueryDTO queryDTO) {
        String sql = String.format(TABLE_IS_IN_SCHEMA, queryDTO.getDbName(), queryDTO.getTableName());
        return sql;
    }

    /**
     * 获取数据源/数据库目录列表的sql，需要实现的数据源去重写该方法
     *
     * @return sql
     */
    protected String getCatalogSql() {
        throw new DtLoaderException(ErrorCode.NOT_SUPPORT.getDesc());
    }

    /**
     * 在字符串前后添加模糊匹配字符
     *
     * @param queryDTO 查询信息
     * @return 添加模糊匹配字符后的字符串
     */
    protected String addFuzzySign(SqlQueryDTO queryDTO) {
        String fuzzySign = getFuzzySign();
        if (Objects.isNull(queryDTO) || StringUtils.isBlank(queryDTO.getTableNamePattern())) {
            return fuzzySign;
        }
        String defaultSign = fuzzySign + queryDTO.getTableNamePattern() + fuzzySign;
        if (!ReflectUtil.fieldExists(SqlQueryDTO.class, "matchType")
                || Objects.isNull(queryDTO.getMatchType())) {
            return defaultSign;
        }
        if (MatchType.ALL.equals(queryDTO.getMatchType())) {
            return queryDTO.getTableNamePattern();
        }
        if (MatchType.PREFIX.equals(queryDTO.getMatchType())) {
            return queryDTO.getTableNamePattern() + fuzzySign;
        }
        if (MatchType.SUFFIX.equals(queryDTO.getMatchType())) {
            return fuzzySign + queryDTO.getTableNamePattern();
        }
        return defaultSign;
    }

    /**
     * 获取模糊匹配字符
     *
     * @return 模糊匹配字符
     */
    protected String getFuzzySign() {
        return "%";
    }

    @Override
    public String getVersion(ISourceDTO source) {
        String showVersionSql = getVersionSql();
        List<String> result = queryWithSingleColumn(source, null, showVersionSql, 1, "failed to get data source version");
        return CollectionUtils.isNotEmpty(result) ? result.get(0) : "";
    }

    /**
     * 获取数据源版本的sql，需要实现的数据源去重写该方法
     *
     * @return 获取版本对应的 sql
     */
    protected String getVersionSql() {
        throw new DtLoaderException(ErrorCode.NOT_SUPPORT.getDesc());
    }

    @Override
    public List<String> listFileNames(ISourceDTO sourceDTO, String path, Boolean includeDir, Boolean recursive, Integer maxNum, String regexStr) {
        throw new DtLoaderException(ErrorCode.NOT_SUPPORT.getDesc());
    }

    @Override
    public Database getDatabase(ISourceDTO sourceDTO, String dbName) {
        AssertUtils.notBlank(dbName, "database name can't be empty.");
        String descDbSql = getDescDbSql(dbName);
        List<Map<String, Object>> result = executeQuery(sourceDTO, SqlQueryDTO.builder().sql(descDbSql).build());
        if (CollectionUtils.isEmpty(result)) {
            throw new DtLoaderException("result is empty when get database info.");
        }
        return parseDbResult(result);
    }

    /**
     * 解析执行结果
     *
     * @param result 执行结果
     * @return db 信息
     */
    public Database parseDbResult(List<Map<String, Object>> result) {
        Map<String, Object> dbInfoMap = result.get(0);
        Database database = new Database();
        database.setDbName(MapUtils.getString(dbInfoMap, DtClassConsistent.PublicConsistent.DB_NAME));
        database.setComment(MapUtils.getString(dbInfoMap, DtClassConsistent.PublicConsistent.COMMENT));
        database.setOwnerName(MapUtils.getString(dbInfoMap, DtClassConsistent.PublicConsistent.OWNER_NAME));
        database.setLocation(MapUtils.getString(dbInfoMap, DtClassConsistent.PublicConsistent.LOCATION));
        return database;
    }

    /**
     * 获取描述数据库信息的 sql，数据源自身取实现
     *
     * @param dbName 数据库名称
     * @return sql for desc database
     */
    public String getDescDbSql(String dbName) {
        throw new DtLoaderException(ErrorCode.NOT_SUPPORT.getDesc());
    }

    /**
     * 获取 schema 名称, 优先从 queryDTO 中取
     *
     * @param sourceDTO 数据源信息
     * @param queryDTO  查询信息
     * @return schema 名称
     */
    protected String getSchema(ISourceDTO sourceDTO, SqlQueryDTO queryDTO) {
        RdbmsSourceDTO rdbmsSourceDTO = (RdbmsSourceDTO) sourceDTO;
        if (Objects.isNull(queryDTO)) {
            return rdbmsSourceDTO.getSchema();
        }
        return StringUtils.isNotBlank(queryDTO.getSchema()) ? queryDTO.getSchema() : rdbmsSourceDTO.getSchema();
    }


    @Override
    public TableInfo getTableInfo(ISourceDTO sourceDTO, String tableName) {
        TableInfo tableInfo = TableInfo.builder().build();
        RdbmsSourceDTO rdbmsSourceDTO = (RdbmsSourceDTO) sourceDTO;
        // 如果返回值只有一个说明不含 schema , 此时获取当前使用的 schema
        List<String> result = StringUtil.splitWithOutQuota(tableName, '.', getSpecialSign());
        if (result.size() == 1) {
            tableInfo.setTableName(result.get(0));
            if (StringUtils.isNotBlank(rdbmsSourceDTO.getSchema())) {
                tableInfo.setSchema(rdbmsSourceDTO.getSchema());
            } else {
                try {
                    // 增加 try catch
                    tableInfo.setSchema(getCurrentSchema(sourceDTO));
                } catch (Exception e) {
                    // ignore error
                    log.warn("get current schema error.", e);
                }
            }
        } else if (result.size() == 2) {
            tableInfo.setSchema(result.get(0));
            tableInfo.setTableName(result.get(1));
        } else {
            throw new DtLoaderException(String.format("tableName:[%s] does not conform to the rule", tableName));
        }
        return tableInfo;
    }

    /**
     * 获取特殊处理关键字、库表名等时的左右符号, 默认使用双引号
     *
     * @return 左右处理符号
     */
    protected Pair<Character, Character> getSpecialSign() {
        return Pair.of('\"', '\"');
    }

    @Override
    public String getCharacterSetByDatabase(ISourceDTO source, SqlQueryDTO queryDTO) {
        throw new DtLoaderException(ErrorCode.NOT_SUPPORT.getDesc());
    }

    @Override
    public String getTimeZoneByDatabase(ISourceDTO source, SqlQueryDTO queryDTO) {
        throw new DtLoaderException(ErrorCode.NOT_SUPPORT.getDesc());
    }

    @Override
    public ColumnMetaDTO getDataType(ISourceDTO source, SqlQueryDTO queryDTO) {
        ColumnMetaDTO columnMetaDTO = new ColumnMetaDTO();
        try {
            CommonDBDataType dataType = CommonDBDataType.getDataType(queryDTO.getColumnType().toUpperCase());
            columnMetaDTO.setDataType(dataType.getDataType());
            columnMetaDTO.setDateType(dataType.getColumnDataType());
        } catch (Exception e) {
            return columnMetaDTO;
        }

        return columnMetaDTO;
    }

    /**
     * 获取数据源动态导入
     *
     * @param source
     * @param queryDTO
     * @return
     * @throws Exception
     */
    @Override
    public DatasourceInfoDTO getDataSourceImport(ISourceDTO source, SqlQueryDTO queryDTO){
        //获取导入对象
        DatasourceInfoImportVO datasourceInfoImportVO = queryDTO.getDatasourceInfoImportVO();
        //拼接jdbcUrl
        String format = getJdbcUrl(datasourceInfoImportVO);

        //获取枚举
        DataSourceTypeEnum dataSourceTypeEnum = DataSourceTypeEnum.typeVersionOf(datasourceInfoImportVO.getDataType(), datasourceInfoImportVO.getDataVersion());
        //构建加密字符串
        Base64DatasourceJson base64DatasourceJson = new Base64DatasourceJson();
        base64DatasourceJson.setDataName(datasourceInfoImportVO.getDataName());
        base64DatasourceJson.setDataTypeCode(dataSourceTypeEnum.getVal());
        base64DatasourceJson.setBusinessUuid(queryDTO.getBusinessUuid());
        if(DataSourceTypeEnum.SQLServer.getDataType().equalsIgnoreCase(dataSourceTypeEnum.getDataType())){
            base64DatasourceJson.setUrlType("1");
        }
        base64DatasourceJson.setDataType(dataSourceTypeEnum.getDataType());
        base64DatasourceJson.setShowDataType(dataSourceTypeEnum.getDataType());
        base64DatasourceJson.setDriverClassName(dataSourceTypeEnum.getDriverClassName());
        base64DatasourceJson.setDataVersion(dataSourceTypeEnum.getDataVersion());
        base64DatasourceJson.setJdbcUrl(format);
        base64DatasourceJson.setSchema(datasourceInfoImportVO.getSchema());
        String userName = datasourceInfoImportVO.getUserName();
        String passWord = datasourceInfoImportVO.getPassWord();
        base64DatasourceJson.setUsername(userName);
        base64DatasourceJson.setPassword(passWord);

        //设置数据源类型编码
        RdbmsSourceDTO rdbmsSourceDTO=(RdbmsSourceDTO)source;
        rdbmsSourceDTO.setPassword(passWord);
        rdbmsSourceDTO.setUsername(userName);
        rdbmsSourceDTO.setUrl(format);
        rdbmsSourceDTO.setSourceType(dataSourceTypeEnum.getVal());

        Integer clearStatus = null;
        Integer status = 0;
        try {
            //清除连接
            clearStatus = beforeQuery(rdbmsSourceDTO, queryDTO, false);
            //测试连接
            Boolean b = testCon(source);
            if (b) {
                //获取版本
                String version = "";
                try {
                    version = getVersion(rdbmsSourceDTO);
                } catch (Exception e) {
                    log.error("获取版本失败{}",datasourceInfoImportVO.getDataName());
                }
                base64DatasourceJson.setDbVersion(version);
//                //获取字符集
//                queryDTO.setSchema(datasourceInfoImportVO.getSchema());
//                String characterSet ="";
//                try {
//                    characterSet = getCharacterSetByDatabase(rdbmsSourceDTO, queryDTO);
//                } catch (Exception e) {
//                    log.error("获取字符集失败{}",datasourceInfoImportVO.getDataName());
//                }
//                String timeZoneByDatabase ="";
//                try {
//                    queryDTO.setSchema(datasourceInfoImportVO.getSchema());
//                    timeZoneByDatabase = getTimeZoneByDatabase(rdbmsSourceDTO, queryDTO);
//                } catch (Exception e) {
//                    log.error("获取时区失败{}",datasourceInfoImportVO.getDataName());
//                }
//                base64DatasourceJson.setCharacterSet(characterSet);
//                //获取时区
//                base64DatasourceJson.setDsTimeZone(timeZoneByDatabase);
                status=1;
            }
        } catch (Exception e) {
            log.error("[{}]数据源连接失败",datasourceInfoImportVO.getDataName());

        } finally {
            DBUtil.closeDBResources(null, null, DBUtil.clearAfterGetConnection(rdbmsSourceDTO, clearStatus));
        }
        //构建数据源对象
        String dataJson = JSONObject.toJSONString(base64DatasourceJson);
        //将json 转为对象
        DatasourceInfoDTO dto = JSON.parseObject(dataJson, DatasourceInfoDTO.class);
        dto.setStatus(status);
        //加密字符串
        dto.setDataJson(ParseDatasourceUtils.getEncodeDataSource(dataJson, true));
        dto.setDbName(datasourceInfoImportVO.getDbName());

        String dataDesc = datasourceInfoImportVO.getDataDesc();
        if(StringUtils.isNotEmpty(dataDesc)){
            dto.setDataDesc(dataDesc);
        }
        dto.setIp(datasourceInfoImportVO.getIp());
        return dto;
    }

    /**
     * 获取数据源版本对应的jdbcUrl
     *
     * @return 获取版本对应的 sql
     */
    protected String getJdbcUrl( DatasourceInfoImportVO datasourceInfoImportVO) {
        throw new DtLoaderException(ErrorCode.NOT_SUPPORT.getDesc());
    }

    /**
     * 获取存储过程
     *
     */
    @Override
    public ProcedureMetadata getProduce(ISourceDTO source, SqlQueryDTO queryDTO) {
        throw new DtLoaderException(ErrorCode.NOT_SUPPORT.getDesc());
    }

    /**
     * 获取存储过程参数
     *
     */
    @Override
    public List<ProcedureMetadataArguments> getProduceArguments(ISourceDTO source, SqlQueryDTO queryDTO) {
        throw new DtLoaderException(ErrorCode.NOT_SUPPORT.getDesc());
    }

    @Override
    public String getTableLabel(ISourceDTO source, SqlQueryDTO queryDTO) {
        throw new DtLoaderException(ErrorCode.NOT_SUPPORT.getDesc());
    }

    @Override
    public String getUrl(ISourceDTO source, SqlQueryDTO queryDTO){
        throw new DtLoaderException(ErrorCode.NOT_SUPPORT.getDesc());
    }


    @Override
    public  List<DbTableVO> getMedataDataTables(ISourceDTO sourceDTO, SqlQueryDTO queryDTO){
        throw new DtLoaderException(ErrorCode.NOT_SUPPORT.getDesc());
    }
    @Override
    public List<String> isTablesExists(ISourceDTO sourceDTO, SqlQueryDTO queryDTO){
        throw new DtLoaderException(ErrorCode.NOT_SUPPORT.getDesc());
    }

    @Override
    public  List<DbTableVO> getIndexList(ISourceDTO sourceDTO, SqlQueryDTO queryDTO){
        throw new DtLoaderException(ErrorCode.NOT_SUPPORT.getDesc());
    }

    @Override
    public   List<ColumnMetaDTO> getIndexColumn(ISourceDTO sourceDTO, SqlQueryDTO queryDTO){
        throw new DtLoaderException(ErrorCode.NOT_SUPPORT.getDesc());
    }
    @Override
    public List<DbTableVO> getFunctionList(ISourceDTO sourceDTO, SqlQueryDTO queryDTO){
        throw new DtLoaderException(ErrorCode.NOT_SUPPORT.getDesc());
    }
    @Override
    public List<ColumnMetaDTO> getFunctionArguments(ISourceDTO sourceDTO, SqlQueryDTO queryDTO){
        throw new DtLoaderException(ErrorCode.NOT_SUPPORT.getDesc());
    }
    @Override
    public Integer getMaxConnections(ISourceDTO sourceDTO) {
        throw new DtLoaderException(ErrorCode.NOT_SUPPORT.getDesc());
    }

    @Override
    public Boolean getMetadataPrivileges(ISourceDTO sourceDTO) throws SQLException {
        throw new DtLoaderException(ErrorCode.NOT_SUPPORT.getDesc());
    }
}
