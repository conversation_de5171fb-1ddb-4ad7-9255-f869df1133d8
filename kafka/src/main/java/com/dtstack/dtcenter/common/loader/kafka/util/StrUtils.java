package com.dtstack.dtcenter.common.loader.kafka.util;

import java.text.DecimalFormat;

/**
 * @描述
 * @创建人 lf
 * @创建时间 2022/9/28
 * @create 2022-09-28-10:40
 */
public class StrUtils {
    private final static long KB_IN_BYTES = 1024;

    private final static long MB_IN_BYTES = 1024 * KB_IN_BYTES;

    private final static long GB_IN_BYTES = 1024 * MB_IN_BYTES;

    private final static long TB_IN_BYTES = 1024 * GB_IN_BYTES;
    private final static DecimalFormat df = new DecimalFormat("0.00");
    /**
     * Formatter string number.
     */
    public static double numberic(String number) {
        DecimalFormat formatter = new DecimalFormat("###.####");
        return Double.valueOf(formatter.format(Double.valueOf(number)));
    }
    /**
     * Formatter customer string number.
     */
    public static double numberic(String number, String format) {
        DecimalFormat formatter = new DecimalFormat(format);
        return Double.valueOf(formatter.format(Double.valueOf(number)));
    }
    /**
     * Formatter byte to kb,mb or gb etc.
     */
    public static String stringify(long byteNumber) {
        if (byteNumber / TB_IN_BYTES > 0) {
            return df.format((double) byteNumber / (double) TB_IN_BYTES) + "TB";
        } else if (byteNumber / GB_IN_BYTES > 0) {
            return df.format((double) byteNumber / (double) GB_IN_BYTES) + "GB";
        } else if (byteNumber / MB_IN_BYTES > 0) {
            return df.format((double) byteNumber / (double) MB_IN_BYTES) + "MB";
        } else if (byteNumber / KB_IN_BYTES > 0) {
            return df.format((double) byteNumber / (double) KB_IN_BYTES) + "KB";
        } else {
            return String.valueOf(byteNumber) + "B";
        }
    }
    /**
     * Assembly number to string.
     */
    public static String assembly(String number) {
        return stringify(integer(numberic(number)));
    }
    /**
     * Convert string number to double.
     */
    public static long integer(double number) {
        return Math.round(number);
    }
}
