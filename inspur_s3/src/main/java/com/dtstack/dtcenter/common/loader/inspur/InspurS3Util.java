/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.dtstack.dtcenter.common.loader.inspur;

import com.amazonaws.ClientConfiguration;
import com.amazonaws.auth.AWSStaticCredentialsProvider;
import com.amazonaws.auth.BasicAWSCredentials;
import com.amazonaws.client.builder.AwsClientBuilder;
import com.amazonaws.regions.Regions;
import com.amazonaws.services.s3.AmazonS3;
import com.amazonaws.services.s3.AmazonS3ClientBuilder;
import com.amazonaws.services.s3.model.*;
import com.dsg.database.datasource.vo.DbTableVO;
import com.dtstack.dtcenter.loader.dto.source.InspurS3SourceDTO;
import com.dtstack.dtcenter.loader.dto.source.ISourceDTO;
import com.dtstack.dtcenter.loader.exception.DtLoaderException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.text.SimpleDateFormat;
import java.util.*;
import java.util.regex.Pattern;

/**
 * aws s3 工具类
 *
 * <AUTHOR>
 * date：Created in 上午10:07 2021/5/6
 * company: www.dtstack.com
 */
@Slf4j
public class InspurS3Util {

    private static final Integer TIMEOUT = 60 * 1000;

    /**
     * 获取 aws s3 客户端
     *
     * @param sourceDTO 数据源信息
     * @return aws s3客户端
     */
    public static AmazonS3 getClient(InspurS3SourceDTO sourceDTO) {
        String region = StringUtils.isNotBlank(sourceDTO.getRegion()) ? sourceDTO.getRegion() : Regions.CN_NORTH_1.getName();
        BasicAWSCredentials credentials = new BasicAWSCredentials(sourceDTO.getAccessKey(), sourceDTO.getSecretKey());
        ClientConfiguration configuration = new ClientConfiguration();
        configuration.setRequestTimeout(TIMEOUT);
        configuration.setClientExecutionTimeout(TIMEOUT);

        AwsClientBuilder.EndpointConfiguration endpointConfiguration = new AwsClientBuilder.EndpointConfiguration(sourceDTO.getEndPoint(),sourceDTO.getRegion());
        return AmazonS3ClientBuilder.standard()
                .withCredentials(new AWSStaticCredentialsProvider(credentials))
                // 设置服务器所属地区
//                .withRegion(region)
                .withEndpointConfiguration(endpointConfiguration)
                .withClientConfiguration(configuration).build();
    }

    /**
     * 关闭 aws s3
     *
     * @param amazonS3 aws s3客户端
     */
    public static void closeAmazonS3(AmazonS3 amazonS3) {
        if (Objects.nonNull(amazonS3)) {
            amazonS3.shutdown();
        }
    }

    /**
     * 强转 sourceDTO 为 InspurS3SourceDTO
     *
     * @param sourceDTO aws s3 sourceDTO
     * @return 转换后的 aws s3 sourceDTO
     */
    public static InspurS3SourceDTO convertSourceDTO(ISourceDTO sourceDTO) {
        if (!(sourceDTO instanceof InspurS3SourceDTO)) {
            throw new DtLoaderException("please pass in InspurS3SourceDTO...");
        }
        return (InspurS3SourceDTO) sourceDTO;
    }

    public static List<String> listSubFolders(ISourceDTO sourceDTO, String bucketName, String prefix) {
        InspurS3SourceDTO s3Source = InspurS3Util.convertSourceDTO(sourceDTO);
        AmazonS3 client = null;
        List<String> result = new ArrayList<>();
        try {
            client = InspurS3Util.getClient(s3Source);
            ListObjectsRequest request = new ListObjectsRequest()
                    .withBucketName(bucketName)
                    .withPrefix(prefix)          // 传空字符串表示根目录
                    .withDelimiter("/");         // 按目录分隔

            ObjectListing listing = client.listObjects(request);
            result.addAll(listing.getCommonPrefixes());
            return result;
        } finally {
            InspurS3Util.closeAmazonS3(client);
        }
    }

    public static List<DbTableVO> getS3MetaData(AmazonS3 s3Client, String bucketName, String prefix,
                                                boolean includeDir, boolean recursive,
                                                Integer maxNum, String regexStr,
                                                String excludePathRegex, boolean stopFlag, int currentLevel) {
        List<DbTableVO> fileMetas = new ArrayList<>();
        if (s3Client == null || StringUtils.isBlank(bucketName)) {
            return fileMetas;
        }

        Queue<String> dirQueue = new LinkedList<>();
        dirQueue.add(prefix == null ? "" : prefix);
        maxNum = maxNum == null ? 10000 : maxNum;

        try {
            while (!dirQueue.isEmpty()) {
                if (stopFlag) {
                    log.info("stopFlag is true, stop searching");
                    break;
                }

                String currentPrefix = dirQueue.poll();

                // 排除路径匹配
                if (StringUtils.isNotBlank(excludePathRegex)) {
                    for (String exclude : excludePathRegex.split(",")) {
                        if (Pattern.compile(exclude).matcher(currentPrefix).matches()) {
                            log.info("Exclude path matched: {}", currentPrefix);
                            continue;
                        }
                    }
                }

                ListObjectsRequest request = new ListObjectsRequest()
                        .withBucketName(bucketName)
                        .withPrefix(currentPrefix)
                        .withDelimiter("/");

                ObjectListing listing = s3Client.listObjects(request);

                String displayName="";
                try {
                    Owner s3AccountOwner = s3Client.getS3AccountOwner();
                    //拥有者名字
                     displayName = s3AccountOwner.getDisplayName();
                }catch (Exception e){
                    log.error("获取用户异常：{}",e.getMessage());
                }
                // 处理目录
                for (String folder : listing.getCommonPrefixes()) {
                    if (includeDir) {
                        DbTableVO vo = new DbTableVO();
                        vo.setName(folder.endsWith("/") ? folder.substring(folder.lastIndexOf("/", folder.length() - 2) + 1, folder.length() - 1) : folder);
                        vo.setFilePath(folder);
                        vo.setType("FOLDER");
                        vo.setFileType("FOLDER");
                        vo.setFileLevel(String.valueOf(currentLevel));
                        vo.setFileParentPath(folder.substring(0, folder.lastIndexOf("/", folder.length() - 2) + 1));
                        vo.setFileOwner(displayName);
                        fileMetas.add(vo);
                    }

                    if (recursive) {
                        dirQueue.add(folder);
                    }
                }

                // 处理文件
                for (S3ObjectSummary summary : listing.getObjectSummaries()) {
                    String key = summary.getKey();
                    if (key.endsWith("/")) continue; // 忽略目录占位符

                    String fileName = key.substring(key.lastIndexOf("/") + 1);
                    if (StringUtils.isNotBlank(regexStr) && !fileName.matches(regexStr)) {
                        continue;
                    }
                    DbTableVO vo = new DbTableVO();
                    vo.setName(fileName);
                    vo.setFilePath(key);
                    vo.setFileSize(String.valueOf(summary.getSize()));
                    vo.setFileModifiedTime(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(summary.getLastModified()));
                    vo.setType("FILE");
                    vo.setFileType(getFileSuffixType(fileName));
                    vo.setFileLevel(String.valueOf(currentLevel));
                    vo.setFileParentPath(key.substring(0, key.lastIndexOf("/")+1));
                    vo.setFileOwner(displayName);
                    fileMetas.add(vo);
                    if (fileMetas.size() >= maxNum) {
                        return fileMetas;
                    }
                }
            }

        } catch (Exception e) {
            log.error("获取 S3 元数据异常: {}", e.getMessage(), e);
        }

        return fileMetas;
    }

    private static String getFileSuffixType(String fileName) {
        if (StringUtils.isBlank(fileName) || !fileName.contains(".")) {
            return "UNKNOWN";
        }
        String suffix = fileName.substring(fileName.lastIndexOf('.') + 1).toLowerCase();
        switch (suffix) {
            case "txt":
                return "TEXT";
            case "csv":
                return "CSV";
            case "json":
                return "JSON";
            case "xml":
                return "XML";
            case "xls":
            case "xlsx":
                return "EXCEL";
            case "zip":
            case "rar":
                return "COMPRESSED";
            default:
                return suffix.toUpperCase(); // 默认返回大写后缀作为类型
        }
    }
}
