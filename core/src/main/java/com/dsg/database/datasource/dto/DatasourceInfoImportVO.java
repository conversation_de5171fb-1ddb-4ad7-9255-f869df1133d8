package com.dsg.database.datasource.dto;

import lombok.Data;

import java.io.Serializable;

/**
 * 数据源导入导出实体对象
 *
 * <AUTHOR>
 * @date 2022-07-07
 */
@Data
public class DatasourceInfoImportVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 序号，用于导出
     */
    private int serNum;

    /**
     * 数据源类型唯一 如Mysql, Oracle, Hive
     */
    private String dataType;

    private String dataVersion;

    /**
     * 数据源类型唯一 如Mysql, Oracle, Hive
     */
    private String urlType;


    /**
     * 数据源名称
     */
    private String dataName;

    /**
     * 数据库ip
     */
    private String ip;

    /**
     * 数据库端口
     */
    private Long port;

    /**
     * 数据库名称
     */
    private String dbName;

    /**
     * 用户名
     */
    private String userName;

    /**
     * 密码
     */
    private String passWord;

    /**
     * 模式名
     */
    private String schema;

    /**
     * 备注
     */
    private String dataDesc;

    private String advancedConfiguration;
}
