package com.dsg.database.datasource.enums;

import com.dsg.database.datasource.exception.ErrorCode;
import com.dsg.database.datasource.exception.PubSvcDefineException;
import com.dtstack.dtcenter.loader.source.DataBaseType;
import com.google.common.collect.HashBasedTable;
import com.google.common.collect.Lists;
import com.google.common.collect.Table;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.util.Strings;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;

/**
 * 数据源类型枚举类 对应{@link com.dtstack.dtcenter.loader.source.DataSourceType}
 *
 * <AUTHOR>
 * @date 2022/7/12
 */
public enum DataSourceTypeEnum {

    /**
     * RDBMS
     */
    MySQL(1, "MySQL", "5.x", DataBaseType.MySql.getDriverClassName(), "`", "`", "`", "`", "mysql5", "jdbc:mysql://%s:%d/%s", "******************************", 3306, true, "mysql"),
    StarRocks(72, "StarRocks", "FOR MySQL5.x", DataBaseType.MySql.getDriverClassName(), "`", "`", "`", "`", "starRocks", "jdbc:mysql://%s:%d/%s", "******************************", 3306, true, "mysql"),
    GoldenDB(73, "GoldenDB", "5.x", DataBaseType.GoldenDB.getDriverClassName(), "`", "`", "`", "`", "goldendb", "jdbc:goldendb://%s:%d/%s", "", 3306, true, "mysql"),
    MySQL8(1001, "MySQL", "8.x", DataBaseType.MySql8.getDriverClassName(), "`", "`", "`", "`", "mysql8", "jdbc:mysql://%s:%d/%s", "******************************", 3306, true, "mysql"),
    MySQL8_HIVE(2024, "MySQL8_HIVE", "", DataBaseType.MySql8.getDriverClassName(), "`", "`", "`", "`", "mysql8_hive", "jdbc:mysql://%s:%d/%s", "******************************", 3306, true, "mysql"),
    MySQL5_HIVE(2025, "MySQL5_HIVE", "", DataBaseType.MySql.getDriverClassName(), "`", "`", "`", "`", "mysql5_hive", "jdbc:mysql://%s:%d/%s", "******************************", 3306, true, "mysql"),
    MySQLPXC(98, "MySQL PXC", null, "", "`", "`", "`", "`", "", "", "", null, false, ""),
    Polardb_For_MySQL(28, "PolarDB for MySQL8", null, DataBaseType.Polardb_For_MySQL.getDriverClassName(), "`", "`", "`", "`", "mysql5", "jdbc:mysql://%s:%d/%s", "******************************", 3306, true, "mysql"),
    Oracle(2, "Oracle", "11.x", DataBaseType.Oracle.getDriverClassName(), "\"", "\"", "\"", "\"", "oracle", "****************************", "***************************************************************************************************************************************)))))", 1521, true, "ORCL"),
    Oracle_9i(2014, "Oracle", "9i", DataBaseType.Oracle.getDriverClassName(), "\"", "\"", "\"", "\"", "oracle_9i", "****************************", "***************************************************************************************************************************************)))))", 1521, true, "ORCL"),
    Oracle_19c(2015, "Oracle", "19c", DataBaseType.Oracle.getDriverClassName(), "\"", "\"", "\"", "\"", "oracle_19c", "****************************", "***************************************************************************************************************************************)))))", 1521, true, "ORCL"),
    SQLServer(3, "SQLServer", "2017_earlier", DataBaseType.SQLServer.getDriverClassName(), "[", "]", "[", "]", "sqlServer", "******************************", "", 1433, false, "master"),
    SQLSERVER_2017_LATER(32, "SQLServer", "2017_later", DataBaseType.SQLSSERVER_2017_LATER.getDriverClassName(), "[", "]", "[", "]", "sqlServer2017", "**************************************", "", 1433, false, "master"),
    PostgreSQL(4, "PostgreSQL", "9.4", DataBaseType.PostgreSQL.getDriverClassName(), "\"", "\"", "\"", "\"", "postgresql", "jdbc:postgresql://%s:%d/%s", "***************************************************************************************************", 5432, true, "postgres"),
    TBase(2018, "TBase", "42.x", DataBaseType.TBase.getDriverClassName(), "\"", "\"", "\"", "\"", "tbase", "jdbc:postgresql://%s:%d/%s", "***************************************************************************************************", 5432, true, "postgres"),
    DWS_PG(2008, "DWS_PG", "9.4", DataBaseType.DWS_PG.getDriverClassName(), "\"", "\"", "\"", "\"", "dws_pg", "jdbc:postgresql://%s:%d/%s", "***************************************************************************************************", 5432, true, "postgres"),
    DWS_MySQL(2009, "DWS_MySQL", "5.x", DataBaseType.DWS_MySQL.getDriverClassName(), "`", "`", "`", "`", "dws_mysql", "jdbc:mysql://%s:%d/%s", "******************************", 3306, true, "mysql"),
    ADB_PostgreSQL(54, "AnalyticDB_PostgreSQL", "9.4", DataBaseType.ADB_FOR_PG.getDriverClassName(), "\"", "\"", "\"", "\"", "adb_postgresql", "jdbc:postgresql://%s:%d/%s", "***************************************************************************************************", 5432, true, "postgres"),
    DB2(19, "DB2", "********", DataBaseType.DB2.getDriverClassName(), "\"", "\"", "\"", "\"", "db2", "jdbc:db2://%s:%d/%s", "", 50000, true, "DB2"),
    DB2_AS400(1009, "DB2_AS400", "5.0", DataBaseType.DB2_AS400.getDriverClassName(), "\"", "\"", "\"", "\"", "db2_as400", "*******************************;", "", 50000, true, "SAMPLE"),
    DMDB(35, "DMDB", "*********", DataBaseType.DMDB.getDriverClassName(), "\"", "\"", "\"", "\"", "dmdb", "jdbc:dm://%s:%d/%s", "", 5236, true, "SYSDBA"),
    //DMDB_FOR_MySQL(35, "DMDB", "For MySQL", DataBaseType.DMDB.getDriverClassName(), "`", "`", "`", "`"),
    RDBMS(5, "RDBMS", null, DataBaseType.RDBMS.getDriverClassName(), "\"", "\"", "\"", "\"", "mysql", "", "", null, false, ""),
    KINGBASE8(40, "KINGBASE8", "V8R6", DataBaseType.KINGBASE8.getDriverClassName(), "\"", "\"", "\"", "\"", "kingbase8", "jdbc:kingbase8://%s:%d/%s", "", 54321, true, "test"),
    Sequoiadb_FOR_MYSQL(1005, "Sequoiadb", "FOR MySQL5.x", DataBaseType.Sequoiadb_FOR_MYSQL.getDriverClassName(), "`", "`", "`", "`", "sequoiadb_for_mysql", "jdbc:mysql://%s:%d/%s", "******************************", 3306, true, "mysql"),

    HIVE1X(27, "Hive", "1.x", DataBaseType.HIVE1X.getDriverClassName(), "`", "`", "`", "`", "hive1", "jdbc:hive2://%s:%d/%s", "", 10000, true, "default"),
    HIVE2X(7, "Hive", "2.x", DataBaseType.HIVE.getDriverClassName(), "`", "`", "`", "`", "hive", "jdbc:hive2://%s:%d/%s", "", 10000, true, "default"),
    HIVE3X(50, "Hive", "3.x", DataBaseType.HIVE3.getDriverClassName(), "`", "`", "`", "`", "hive3", "jdbc:hive2://%s:%d/%s", "", 10000, true, "default"),
    HIVE3X_MRS(74, "Hive", "3.x.mrs", DataBaseType.HIVE3_MRS.getDriverClassName(), "`", "`", "`", "`", "huawei_hive3", "jdbc:hive2://%s:%d/%s", "", 10000, true, "default"),
    SparkThrift2_1(45, "SparkThrift", null, "", "", "", "", "", "spark", "", "", null, false, ""),
    MAXCOMPUTE(10, "Maxcompute", "ODPS", DataBaseType.MaxCompute.getDriverClassName(), "`", "`", "`", "`", "maxcompute", "", "", null, false, ""),
    GREENPLUM6(36, "Greenplum", "6.x", DataBaseType.Greenplum6.getDriverClassName(), "\"", "\"", "\"", "\"", "greenplum6", "**********************************************", "", 5432, true, "postgres"),
    GREENPLUM_PostgreSQL(66, "Greenplum_PostgreSQL", "9.4", DataBaseType.GP_FOR_PG.getDriverClassName(), "\"", "\"", "\"", "\"", "greenplum_postgresql", "jdbc:postgresql://%s:%d/%s", "", 5432, true, "postgres"),
    LIBRA(21, "GaussDB", null, DataBaseType.LIBRA.getDriverClassName(), "\"", "\"", "\"", "\"", "libra", "jdbc:postgresql://%s:%d/%s", "", 5432, true, "postgres"),
    GBase_8a(22, "GBase", "8a", DataBaseType.GBase8a.getDriverClassName(), "`", "`", "`", "`", "gbase", "jdbc:gbase://%s:%d/%s", "", 5258, true, ""),
    HDFS(6, "HDFS", "2.x", "", "", "", "", "", "hdfs", "hdfs://%s:%d", "", 8020, false, ""),
    HDFS3(69, "HDFS3", "3.x", "", "", "", "", "", "hdfs3", "hdfs://%s:%d", "", 8020, false, ""),
    HDFS_MRS(2021, "HDFS", "MRS", "", "", "", "", "", "huawei_hdfs", "hdfs://%s:%d", "", 8020, false, ""),
    HDFS_TBDS(60, "HDFS", "TBDS", "", "", "", "", "", "tbds_hdfs", "hdfs://%s:%d", "", 8020, false, ""),
    FTP(9, "FTP", "0.1.55", "", "", "", "", "", "ftp", "ftp://%s:%d", "", 21, false, ""),
    SFTP(2005, "SFTP", "0.1.55", "", "", "", "", "", "ftp", "sftp://%s:%d/%s", "", 22, false, ""),
    HOST(1008, "HOST", "0.1.55", "", "", "", "", "", "ftp", "", "", null, false, ""),
    IMPALA(29, "Impala", "2.6.x", DataBaseType.Impala.getDriverClassName(), "`", "`", "`", "`", "impala", "jdbc:impala://%s:%d/%s", "", 21000, true, ""),
    ClickHouse(25, "ClickHouse", "21.3", DataBaseType.Clickhouse.getDriverClassName(), "`", "`", "`", "`", "clickhouse", "jdbc:clickhouse://%s:%d/%s", "", 8123, true, "default"),
    ClickHouse_MRS(2023, "ClickHouse", "MRS", DataBaseType.Clickhouse_Mrs.getDriverClassName(), "`", "`", "`", "`", "huawei_clickhouse", "jdbc:clickhouse://%s:%d/%s", "", 8123, true, ""),
    TiDB(31, "TiDB", "FOR MySQL5.x", DataBaseType.TiDB.getDriverClassName(), "`", "`", "`", "`", "tidb", "jdbc:mysql://%s:%d/%s", "******************************", 3306, true, "mysql"),
    CarbonData(20, "CarbonData", null, DataBaseType.CarbonData.getDriverClassName(), "\"", "\"", "\"", "\"", "hive", "jdbc:hive2://%s:%d/%s", "", 10000, true, ""),
    Kudu(24, "Kudu", "1.10.1", DataBaseType.Kudu.getDriverClassName(), "`", "`", "`", "`", "kudu", "%s:%d", "", 7051, false, ""),
    Kylin(58, "Kylin URL", "3.x", DataBaseType.Kylin.getDriverClassName(), "`", "`", "`", "`", "kylin", "", "", null, false, ""),
    HBASE(8, "HBase", "1.x", "", "", "", "", "", "hbase", "%s:%d", "", 2181, false, ""),
    HBASE2(39, "HBase", "2.x", "", "", "", "", "", "hbase2", "%s:%d", "", 2181, false, ""),
    HBASE_TBDS(61, "HBase", "TBDS", "", "", "", "", "", "tbds_hbase", "%s:%d", "", 2181, false, ""),
    HBASE_MRS(2022, "HBase", "MRS", "", "", "", "", "", "huawei_hbase", "%s:%d", "", 2181, false, ""),
    Phoenix4(30, "Phoenix", "4.x", DataBaseType.Phoenix.getDriverClassName(), "`", "`", "`", "`", "phoenix", "", "", null, false, ""),
    Phoenix5(38, "Phoenix", "5.x", DataBaseType.Phoenix5.getDriverClassName(), "`", "`", "`", "`", "phoenix5", "", "", null, false, ""),
    ES(11, "Elasticsearch", "5.x", "", "", "", "", "", "es5", "%s:%d", "", 9200, false, ""),
    ES6(33, "Elasticsearch", "6.x", "", "", "", "", "", "es", "%s:%d", "", 9200, false, ""),
    ES7(46, "Elasticsearch", "7.x", "", "", "", "", "", "es7", "%s:%d", "", 9200, false, ""),
    ES8(2033, "Elasticsearch", "8.x", "", "", "", "", "", "es8", "%s:%d", "", 9200, false, ""),
    MONGODB(13, "MongoDB", "3.12.4", "", "", "", "", "", "mongo", "%s:%d/%s", "", 27017, true, ""),
    SAP_HANA(76, "SAP_HANA", "2.4.51", DataBaseType.sapHana1.getDriverClassName(), "\"", "\"", "\"", "\"", "sap_hana", "jdbc:sap://%s:%d/%s", "", 39015, false, ""),
    REDIS(12, "Redis", "3.0_later", "", "", "", "", "", "redis", "%s:%d", "", 6379, false, ""),
    S3(41, "S3", null, "", "", "", "", "", "s3", "", "", null, false, ""),
    KAFKA_TBDS(62, "Kafka", "TBDS", "", "", "", "", "", "tbds_kafka", "%s:%d", "", 9092, false, ""),
    KAFKA(26, "Kafka", "1.x", "", "", "", "", "", "kafka", "%s:%d", "", 9092, false, ""),
    KAFKA_2X(37, "Kafka", "2.x", "", "", "", "", "", "kafka", "%s:%d", "", 9092, false, ""),
    KAFKA_09(18, "Kafka", "0.9", "", "", "", "", "", "kafka", "%s:%d", "", 9092, false, ""),
    KAFKA_10(17, "Kafka", "0.10", "", "", "", "", "", "kafka", "%s:%d", "", 9092, false, ""),
    KAFKA_11(14, "Kafka", "0.11", "", "", "", "", "", "kafka", "%s:%d", "", 9092, false, ""),
    HIGH_VERSION_KAFKA(68, "Kafka", "2.0", "", "", "", "", "", "high_version_kafka", "%s:%d", "", 9092, false, ""),
    EMQ(34, "EMQ", null, "", "", "", "", "", "emq", "", "", null, false, ""),
    WEB_SOCKET(42, "WebSocket", null, "", "", "", "", "", "websocket", "", "", null, false, ""),
    VERTICA(43, "Vertica", null, "", "", "", "", "", "vertica", "", "", null, false, ""),
    SOCKET(44, "Socket", null, "", "", "", "", "", "socket", "", "", null, false, ""),
    ADS(15, "AnalyticDB_MySQL", "5.x", DataBaseType.ADS.getDriverClassName(), "`", "`", "`", "`", "analyticdb_mysql", "jdbc:mysql://%s:%d/%s", "******************************", 3306, true, "mysql"),
    Presto(48, "Presto", null, DataBaseType.Presto.getDriverClassName(), "`", "`", "`", "`", "presto", "", "", null, false, ""),
    SOLR(53, "Solr", "7.x", "", "", "", "", "", "solr", "", "", null, false, ""),
    INFLUXDB(55, "InfluxDB", "1.x", "", "", "", "", "", "influxdb", "", "", null, false, ""),
    INCEPTOR(52, "Inceptor", "6.0.2", DataBaseType.INCEPTOR.getDriverClassName(), "`", "`", "`", "`", "inceptor", "jdbc:hive2://%s:%d/%s", "", 10000, true, ""),
    INCEPTOR8(2012, "Inceptor", "8.x", DataBaseType.INCEPTOR8.getDriverClassName(), "`", "`", "`", "`", "inceptor8", "jdbc:hive2://%s:%d/%s", "", 10000, true, ""),
    AWS_S3(51, "AWS_S3", "1.11.636", "", "", "", "", "", "aws_s3", "http://%s:%d", "", 9000, false, ""),
    OSS_ALI(2006, "ALI_OSS", "3.13.1", "", "", "", "", "", "oss_ali", "http://%s:%d", "", 80, false, ""),
    DATA_HUB(2013, "DataHub", "2.x", "", "", "", "", "", "datahub", "http://%s:%d", "", 80, false, ""),
    OSS_HUAWEI(2011, "HUAWEI_OBS", "3.21.4", "", "", "", "", "", "oss_huawei", "http://%s:%d", "", 80, false, ""),
    OSS_LC(2010, "INSPUR_OSS", "4.0.19", "", "", "", "", "", "oss_lc", "", "", null, false, ""),
    OPENTSDB(56, "OpenTSDB", "2.x", "", "", "", "", "", "opentsdb", "", "", null, false, ""),
    Doris_JDBC(57, "Doris", "0.x", DataBaseType.Doris.getDriverClassName(), "`", "`", "`", "`", "doris", "jdbc:mysql://%s:%d/%s", "******************************", 9030, true, "mysql"),
    Kylin_Jdbc(23, "Kylin JDBC", "3.x", "", "", "", "", "", "kylin", "", "", null, false, ""),
    OceanBase_FOR_MySQL(49, "OceanBase_FOR_MySQL", "4.x", DataBaseType.OceanBase.getDriverClassName(), "`", "`", "`", "`", "oceanbase", "jdbc:oceanbase://%s:%d/%s", "", 2881, false, ""),
    OceanBase_FOR_ORACLE(1006, "OceanBase_FOR_ORACLE", "4.x", DataBaseType.OceanBase.getDriverClassName(), "\"", "\"", "\"", "\"", "oceanbase_for_oracle", "jdbc:oceanbase://%s:%d/%s", "", 2881, false, "SYS"),
    RESTFUL(47, "Restful", null, "", "", "", "", "", "restful", "", "", null, false, ""),
    TRINO(59, "Trino", null, DataBaseType.TRINO.getDriverClassName(), "", "", "", "", "trino", "", "", null, false, ""),
    DORIS_HTTP(64, "Doris", "HTTP", "", "", "", "", "", "dorisrestful", "", "", null, false, ""),
    DORIS1(2016, "Doris", "1.x", DataBaseType.Doris.getDriverClassName(), "`", "`", "`", "`", "doris", "jdbc:mysql://%s:%d/%s", "******************************", 9030, true, "mysql"),
    DORIS2(2017, "Doris", "2.x", DataBaseType.Doris.getDriverClassName(), "`", "`", "`", "`", "doris", "jdbc:mysql://%s:%d/%s", "******************************", 9030, true, "mysql"),
    DMDB_FOR_ORACLE(67, "DMDB", "For Oracle", DataBaseType.DMDB_For_Oracle.getDriverClassName(), "\"", "\"", "\"", "\"", "dmdb", "jdbc:dm://%s:%d/%s", "", 5236, true, "SYSDBA"),
    MariaDB(63, "MariaDB", "11.2.1", DataBaseType.MariaDB.getDriverClassName(), "`", "`", "`", "`", "mariadb", "jdbc:mysql://%s:%d/%s", "******************************", 3306, true, "mysql"),
    TDSQL_FOR_MySQL(1007, "TDSQL_FOR_MySQL", "8.x", DataBaseType.MySql.getDriverClassName(), "`", "`", "`", "`", "tdsql_for_mysql", "jdbc:mysql://%s:%d/%s", "******************************", 3306, true, "mysql"),
    TDSQL_FOR_PG(2019, "TDSQL_FOR_PG", "42.x", DataBaseType.TDSQL_FOR_PG.getDriverClassName(), "\"", "\"", "\"", "\"", "tdsql_for_pg", "jdbc:postgresql://%s:%d/%s", "***************************************************************************************************", 5432, true, "postgres"),
    TDSQL_FOR_ORACLE(2020, "TDSQL_FOR_ORACLE", "42.x", DataBaseType.TDSQL_FOR_ORACLE.getDriverClassName(), "\"", "\"", "\"", "\"", "tdsql_for_oracle", "jdbc:postgresql://%s:%d/%s", "***************************************************************************************************", 5432, true, "postgres"),
    GaussDB(2000, "GaussDB", "5.0.0", DataBaseType.GaussDB.getDriverClassName(), "\"", "\"", "\"", "\"", "gaussdb", "jdbc:postgresql://%s:%d/%s", "***************************************************************************************************", 5432, true, "postgres"),
    GaussDB_HIVE(2026, "GaussDB_HIVE", "", DataBaseType.GaussDB.getDriverClassName(), "\"", "\"", "\"", "\"", "gaussdb_hive", "jdbc:postgresql://%s:%d/%s", "***************************************************************************************************", 5432, true, "postgres"),
    Gauss_DB200(2007, "Gauss_DB200", "2.0", DataBaseType.Gauss_DB200.getDriverClassName(), "\"", "\"", "\"", "\"", "gauss_db200", "jdbc:gaussdb://%s:%d/%s", "", 5432, true, "postgres"),
    GaussDB_FOR_MySQL(2003, "GaussDB_FOR_MySQL", "5.x", DataBaseType.GaussDB_FOR_MySQL.getDriverClassName(), "`", "`", "`", "`", "gaussdb_for_mysql", "jdbc:mysql://%s:%d/%s", "******************************", 3306, true, "mysql"),
    DDM_FOR_MYSQL(2001, "ddm_for_mysql", null, DataBaseType.DDM_FOR_MYSQL.getDriverClassName(), "`", "`", "`", "`", "", "", "", null, false, ""),
    ArgoDB(2002, "ArgoDB", "6.0.0", DataBaseType.ArgoDB.getDriverClassName(), "`", "`", "`", "`", "argodb", "jdbc:hive2://%s:%d/%s", "", 10000, true, "default"),
    Informix(1003, "Informix", "10.x~14.x", DataBaseType.Informix.getDriverClassName(), "\"", "\"", "\"", "\"", "informix", "jdbc:informix-sqli://%s:%d/%s:INFORMIXSERVER=%s", "", 9088, true, "sysmaster"),
    KunDB(1004, "KunDB", "3.0", DataBaseType.Informix.getDriverClassName(), "`", "`", "`", "`", "kundb", "jdbc:informix-sqli://%s:%d/%s:INFORMIXSERVER=%s", "", 9088, true, "sysmaster"),
    Sybase(2030, "Sybase", "JTDS-1.X", DataBaseType.Sybase_jTDS.getDriverClassName(), "\"", "\"", "\"", "\"", "sybase_jtds", "****************************************", "", 5000, true, ""),
    Sybase_Jconn(2031, "Sybase", "Jconn4", DataBaseType.Sybase_jConnect.getDriverClassName(), "\"", "\"", "\"", "\"", "sybase_jconnect", "************************", "", 5000, true, "master"),
    INSPUR_S3(2032, "INSPUR_S3", "1.X", "", "", "", "", "", "inspur_s3", "http://%s:%d", "", null, false, ""),
    ;

    private static final Table<String, String, DataSourceTypeEnum> CACHE = HashBasedTable.create();
    private static final Table<String, String, DataSourceTypeEnum> CACHE_LOWER = HashBasedTable.create();
    private static final Map<String, String> CACHE_LOWER_MAP = new HashMap<>();
    private static final Map<String, List<String>> CACHE_LOWER_VERSION = new HashMap<>();

    // 构建映射表以提高查找效率
    private static final Map<String, DataSourceTypeEnum> dataTypeMap = new HashMap<>();


    static {
        for (DataSourceTypeEnum value : DataSourceTypeEnum.values()) {
            dataTypeMap.put(value.getDataType().toLowerCase(), value);
            CACHE.put(value.getDataType(), Objects.isNull(value.getDataVersion()) ? StringUtils.EMPTY : value.getDataVersion(), value);
            CACHE_LOWER.put(value.getDataType().toLowerCase(), Objects.isNull(value.getDataVersion()) ? StringUtils.EMPTY : value.getDataVersion(), value);
            CACHE_LOWER_MAP.put(value.getDataType().toLowerCase(), value.getDataType());
            List<String> list = new ArrayList<>();
            if (CACHE_LOWER_VERSION.containsKey(value.getDataType().toLowerCase())) {
                List<String> strings = CACHE_LOWER_VERSION.get(value.getDataType().toLowerCase());
                strings.add(value.getDataVersion());
                CACHE_LOWER_VERSION.put(value.getDataType().toLowerCase(), strings);
            } else {
                list.add(value.getDataVersion());
                CACHE_LOWER_VERSION.put(value.getDataType().toLowerCase(), list);
            }
        }
    }

    private static final Logger LOGGER = LoggerFactory.getLogger(DataSourceTypeEnum.class);

    DataSourceTypeEnum(Integer val,
                       String dataType,
                       String dataVersion,
                       String driverClassName,
                       String tablePrefix,
                       String tableSuffix,
                       String columnPrefix,
                       String columnSuffix,
                       String pluginName,
                       String urlPattern,
                       String multiNodeUrlPattern,
                       Integer defaultPort,
                       Boolean requiresDatabase,
                       String defaultDatabase) {
        this.val = val;
        this.dataType = dataType;
        this.dataVersion = dataVersion;
        this.driverClassName = driverClassName;
        this.tablePrefix = tablePrefix;
        this.tableSuffix = tableSuffix;
        this.columnPrefix = columnPrefix;
        this.columnSuffix = columnSuffix;
        this.pluginName = pluginName;
        this.urlPattern = urlPattern;
        this.multiNodeUrlPattern = multiNodeUrlPattern;
        this.defaultPort = defaultPort;
        this.requiresDatabase = requiresDatabase;
        this.defaultDatabase = defaultDatabase;
    }

    /**
     * 根据数据源类型和版本获取数据源枚举信息
     *
     * @param dataType
     * @param dataVersion
     * @return
     */
    public static DataSourceTypeEnum typeVersionOf(String dataType, String dataVersion) {
        if (StringUtils.isBlank(dataVersion)) {
            dataVersion = StringUtils.EMPTY;
        }
        DataSourceTypeEnum value;
        if ((value = CACHE.get(dataType, dataVersion)) == null) {
            LOGGER.error("this dataType cannot find,dataType:{},dataVersion:{}", dataType, dataVersion);
            throw new PubSvcDefineException(ErrorCode.CAN_NOT_FITABLE_SOURCE_TYPE);
        }
        return value;
    }

    /**
     * 根据数据源类型和版本获取数据源枚举信息
     *
     * @param dataType 数据源类型
     * @return 对应的枚举值或默认值
     */
    public static DataSourceTypeEnum getDbEnum(String dataType) {
        if (dataType == null) {
            throw new IllegalArgumentException("Data type cannot be null");
        }
        DataSourceTypeEnum result = dataTypeMap.get(dataType.toLowerCase());
        return result;
    }

    /**
     * 根据数据源类型和版本获取数据源枚举信息--类型小写
     *
     * @param dataType
     * @param dataVersion
     * @return
     */
    public static DataSourceTypeEnum lowerTypeVersionOf(String dataType, String dataVersion) {
        if (StringUtils.isBlank(dataVersion)) {
            dataVersion = StringUtils.EMPTY;
        }
        DataSourceTypeEnum value;
        if ((value = CACHE_LOWER.get(dataType, dataVersion)) == null) {
            LOGGER.error("this dataType cannot find,dataType:{},dataVersion:{}", dataType, dataVersion);
            throw new PubSvcDefineException(ErrorCode.CAN_NOT_FITABLE_SOURCE_TYPE);
        }
        return value;
    }

    /**
     * 根据数据源类型和版本获取数据源枚举信息--类型小写
     *
     * @param dataType
     * @return
     */
    public static List<String> versions(String dataType) {
        List<String> versions = Lists.newArrayList();
        if ((versions = CACHE_LOWER_VERSION.get(dataType)) == null) {
            LOGGER.error("this dataType cannot find,dataType:{}", dataType);
        }
        return versions;
    }

    /**
     * 根据数据源类型和版本获取数据源枚举信息--类型小写
     *
     * @param dataType
     * @return
     */
    public static String lowerTypeVersionOf(String dataType) {
        String value = "";
        if ((value = CACHE_LOWER_MAP.get(dataType)) == null) {
            LOGGER.error("this dataType cannot find,dataType:{}", dataType);
        }
        return value;
    }


    /**
     * 根据数据源val获取数据源枚举信息
     *
     * @param val
     * @return
     */
    public static DataSourceTypeEnum valOf(Integer val) {
        Objects.requireNonNull(val);
        for (DataSourceTypeEnum value : DataSourceTypeEnum.values()) {
            if (Objects.equals(value.getVal(), val)) {
                return value;
            }
        }
        LOGGER.error("can not find this dataTypeCode:{}", val);
        throw new PubSvcDefineException(ErrorCode.CAN_NOT_FITABLE_SOURCE_TYPE);
    }

    /**
     * 数据源值
     */
    private Integer val;
    /**
     * 数据源类型
     */
    private String dataType;
    /**
     * 数据源版本(可为空)
     */
    private String dataVersion;
    /**
     * 驱动名（来源：com.dtstack.dtcenter.loader.source.DataBaseType）
     */
    private String driverClassName;

    /**
     * 库表的标志符
     */
    private String tablePrefix;
    private String tableSuffix;
    /**
     * 字段的标志符
     */
    private String columnPrefix;
    private String columnSuffix;

    /**
     * 插件文件夹名称
     */
    private String pluginName;
    /**
     * 连接URL格式
     */
    private String urlPattern;
    /**
     * 多节点连接URL格式
     */
    private String multiNodeUrlPattern;
    /**
     * 默认端口号
     */
    private Integer defaultPort;
    /**
     * 是否必须指定数据库名
     */
    private Boolean requiresDatabase;
    /**
     * 默认数据库
     */
    private String defaultDatabase;

    public Integer getVal() {
        return val;
    }

    public void setVal(Integer val) {
        this.val = val;
    }

    public String getDataType() {
        return dataType;
    }

    public void setDataType(String dataType) {
        this.dataType = dataType;
    }

    public String getDataVersion() {
        return dataVersion;
    }

    public void setDataVersion(String dataVersion) {
        this.dataVersion = dataVersion;
    }

    public String getDriverClassName() {
        return driverClassName;
    }

    public void setDriverClassName(String driverClassName) {
        this.driverClassName = driverClassName;
    }

    public String getTablePrefix() {
        return tablePrefix;
    }

    public void setTablePrefix(String tablePrefix) {
        this.tablePrefix = tablePrefix;
    }

    public String getTableSuffix() {
        return tableSuffix;
    }

    public void setTableSuffix(String tableSuffix) {
        this.tableSuffix = tableSuffix;
    }

    public String getColumnPrefix() {
        return columnPrefix;
    }

    public void setColumnPrefix(String columnPrefix) {
        this.columnPrefix = columnPrefix;
    }

    public String getColumnSuffix() {
        return columnSuffix;
    }

    public void setColumnSuffix(String columnSuffix) {
        this.columnSuffix = columnSuffix;
    }

    public String getPluginName() {
        return pluginName;
    }

    public void setPluginName(String pluginName) {
        this.pluginName = pluginName;
    }

    public String getUrlPattern() {
        return urlPattern;
    }

    public void setUrlPattern(String urlPattern) {
        this.urlPattern = urlPattern;
    }

    public String getMultiNodeUrlPattern() {
        return multiNodeUrlPattern;
    }

    public void setMultiNodeUrlPattern(String multiNodeUrlPattern) {
        this.multiNodeUrlPattern = multiNodeUrlPattern;
    }

    public Integer getDefaultPort() {
        return defaultPort;
    }

    public void setDefaultPort(Integer defaultPort) {
        this.defaultPort = defaultPort;
    }

    public Boolean getRequiresDatabase() {
        return requiresDatabase;
    }

    public void setRequiresDatabase(Boolean requiresDatabase) {
        this.requiresDatabase = requiresDatabase;
    }

    public String getDefaultDatabase() {
        return defaultDatabase;
    }

    public void setDefaultDatabase(String defaultDatabase) {
        this.defaultDatabase = defaultDatabase;
    }

    /**
     * 根据数据库类型拼接特殊字符，如查询中文表名需要加双引号
     *
     * @param value 传入的表名或者库名
     * @return 拼接字符后的值
     */
    public String getTablePrefixAndSuffixValue(String value) {
        return String.format("%s%s%s", this.tablePrefix, value, this.tableSuffix);
    }

    /**
     * 根据数据库类型拼接特殊字符，如查询中文字段名需要加双引号
     *
     * @param value 传入的字段名
     * @return 拼接字符后的值
     */
    public String getColumnPrefixAndSuffixValue(String value) {
        return String.format("%s%s%s", this.columnPrefix, value, this.columnSuffix);
    }

    @Override
    public String toString() {
        if (Strings.isNotBlank(dataVersion)) {
            return dataType + "-" + dataVersion;
        }
        return dataType;
    }
}
