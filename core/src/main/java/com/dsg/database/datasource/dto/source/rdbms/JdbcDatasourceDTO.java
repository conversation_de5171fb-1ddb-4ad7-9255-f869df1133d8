package com.dsg.database.datasource.dto.source.rdbms;

import com.dsg.database.datasource.dto.source.BaseDatasourceDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * jdbc系列数据源类型配置参数
 * 目前包括：MySQL，Oracle，ClickHouse
 *
 * <AUTHOR>
 * @date 2022/7/8 16:48
 */
@Data
@EqualsAndHashCode(callSuper = true)
public abstract class JdbcDatasourceDTO extends BaseDatasourceDTO {

    private static final long serialVersionUID = 1L;

    /**
     * JDBC URL
     */
    private String jdbcUrl;
    /**
     * 用户名
     */
    private String username;
    /**
     * 密码
     */
    private String password;
}
