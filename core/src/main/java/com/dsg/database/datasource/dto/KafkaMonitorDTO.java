package com.dsg.database.datasource.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @描述
 * @创建人 lf
 * @创建时间 2022/10/13
 * @create 2022-10-13-11:32
 */
@AllArgsConstructor
@NoArgsConstructor
@Data
@Builder
public class KafkaMonitorDTO {

    private String fifteenMinute;
    private String fiveMinute;
    private String meanRate;
    private String oneMinute;
    private String rate;

}
