package com.dsg.database.datasource.utils;

import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * jdbcUrl工具类
 *
 * <AUTHOR>
 * @date 2022/11/8 15:58
 */
public class JdbcUrlUtils {
    /**
     * 根据jdbcUrl返回端口号port
     * 注意：SqlServer的jdbc连接格式如下，需要特殊处理，
     * ***************************************************
     * 或 *******************************************    这种格式可以走通用的解析
     *
     * @param url jdbc连接信息
     * @return 端口号
     */
    public static int getPort(String url) {
        int port = 0;
        if (StringUtils.isNotBlank(url)) {
            int begin = url.indexOf("//");
            int end;
            if (StringUtils.startsWithIgnoreCase(url.trim(), "jdbc:sqlserver") ||StringUtils.startsWithIgnoreCase(url.trim(), "jdbc:jtds:sybase") ) {
                end = url.lastIndexOf(";");
            } else if (StringUtils.startsWithIgnoreCase(url.trim(), "jdbc:oracle:thin:@") && !url.contains("//")) {
                //oracle sid连接方式
                begin = url.indexOf("@");
                end = url.lastIndexOf(":");
            } else if (StringUtils.startsWithIgnoreCase(url.trim(), "jdbc:pivotal:greenplum")) {
                //greenplum连接方式
                end = url.lastIndexOf(";");
            } else if(  StringUtils.startsWithIgnoreCase(url.trim(), "jdbc:as400")){
                String[] split = url.split(";");
                if(split.length>0){
                    String url_new = split[0]+";";
                    end = url_new.lastIndexOf(";");
                }else{
                    end = url.lastIndexOf(";");
                }
            } else if(StringUtils.startsWithIgnoreCase(url.trim(), "jdbc:sybase:Tds")){
                // 正则获取port
                String regex = "jdbc:sybase:Tds:([0-9]{1,3}\\.[0-9]{1,3}\\.[0-9]{1,3}\\.[0-9]{1,3}|[a-zA-Z0-9\\.\\-]+):([0-9]+)";
                Pattern pattern = Pattern.compile(regex);
                Matcher matcher = pattern.matcher(url);
                if (matcher.find()) {
                    String portStr = matcher.group(2);
                    return Integer.parseInt(portStr);
                }
                end = url.lastIndexOf("/");
            } else {
                end = url.lastIndexOf("/");
                if (end < begin + 2) {
                    end = url.length() - 1;
                }
            }

            if (begin > -1 && end > -1 && end > begin) {
                if (StringUtils.startsWithIgnoreCase(url.trim(), "jdbc:oracle:thin:@") && !url.contains("//")) {
                    return Integer.parseInt(url.substring(begin + 1, end).split(":")[1]);
                }
                String hostStr = url.substring(begin + 2, end + 1);
                int index = hostStr.indexOf(":");
                int indexE;
                if (StringUtils.startsWithIgnoreCase(url.trim(), "jdbc:sqlserver") ||
                        StringUtils.startsWithIgnoreCase(url.trim(), "jdbc:jtds:sybase")||
                        StringUtils.startsWithIgnoreCase(url.trim(), "jdbc:pivotal:greenplum") ||
                        StringUtils.startsWithIgnoreCase(url.trim(), "jdbc:as400")) {
                    indexE = hostStr.indexOf(";");
                } else {
                    indexE = hostStr.indexOf("/");
                    if (indexE < 0) {
                        indexE = hostStr.length();
                    }
                }
                if (index > 0) {
                    String substring = hostStr.substring(index + 1, indexE);
                    if (substring.contains(",")) {
                        substring = substring.substring(0, substring.indexOf(","));
                    }
                    port = Integer.parseInt(substring);
                } else {
                    port = Integer.parseInt(hostStr);
                }

            }
        }
        return port;
    }

    /**
     * 根据jdbcUrl返回ip
     * 注意：SqlServer的jdbc连接格式如下，需要特殊处理，
     * ***************************************************
     * 或 *******************************************    这种格式可以走通用的解析
     *
     * @param url jdbc连接信息
     * @return ip
     */
    public static String getIp(String url) {
        String ip = null;
        if (StringUtils.isNotBlank(url)) {
            int begin = url.indexOf("//");
            int end;
            if (StringUtils.startsWithIgnoreCase(url.trim(), "jdbc:oracle:thin:@") && !url.contains("//")) {
                //oracle sid连接方式
                begin = url.indexOf("@");
                end = url.substring(0, url.lastIndexOf(":")).lastIndexOf(":");
            } else {
                if (StringUtils.startsWithIgnoreCase(url.trim(), "jdbc:sqlserver") ||
                        StringUtils.startsWithIgnoreCase(url.trim(), "jdbc:jtds:sybase")||
                        StringUtils.startsWithIgnoreCase(url.trim(), "jdbc:pivotal:greenplum")
                       ) {
                    end = url.lastIndexOf(";");
                } else if( StringUtils.startsWithIgnoreCase(url.trim(), "jdbc:as400")){
                    String[] split = url.split(";");
                    if(split.length>0){
                        String url_new = split[0]+";";
                        end = url_new.lastIndexOf(";");
                    }else{
                        end = url.lastIndexOf(";");
                    }
                } else if (StringUtils.startsWithIgnoreCase(url.trim(), "jdbc:sybase:Tds:")) {
                    // 正则获取ip/host
                    String regex = "jdbc:sybase:Tds:([0-9]{1,3}\\.[0-9]{1,3}\\.[0-9]{1,3}\\.[0-9]{1,3}|[a-zA-Z0-9\\.\\-]+):([0-9]+)";
                    Pattern pattern = Pattern.compile(regex);
                    Matcher matcher = pattern.matcher(url);
                    if (matcher.find()) {
                        ip = matcher.group(1);
                    }
                    return ip;
                } else {
                    end = url.lastIndexOf("/");
                    if (end < begin + 2) {
                        end = url.length();
                    }
                }
            }
            if (begin > -1 && end > -1 && end > begin) {
                String hostStr = url.substring(begin + 2, end);
                if (StringUtils.startsWithIgnoreCase(url.trim(), "jdbc:oracle:thin:@") && !url.contains("//")) {
                    hostStr = url.substring(begin + 1, end);
                }
                int index = hostStr.indexOf(":");
                if (index > 0) {
                    ip = hostStr.substring(0, index);
                } else {
                    ip = hostStr;
                }

            }
        }
        return ip;
    }
    public static List<String> getHosts(String url) {
        // 正则：同时匹配域名和 IPv4
        String regex =
                "(?i)" +
                        "\\b" + // 单词边界，避免匹配部分内容（如 "example.comx"）
                        "(" +
                        "(?:[a-z0-9-]+\\.)+[a-z]{2,}" +  // 域名（如 example.com, sub.domain.co.uk）
                        "|" +
                        "(?:25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)(?:\\.(?:25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)){3}" + // IPv4
                        ")" +
                        "\\b"; // 结束边界
        Pattern pattern = Pattern.compile(regex);
        Matcher matcher = pattern.matcher(url);
        List<String> ipList = new ArrayList<>();
        while (matcher.find()) {
            ipList.add(matcher.group());
        }
        return ipList;
    }
    public static List<String> getPorts(String url) {
        String regex = ":(\\d+)|PORT=(\\d+)";
        Pattern pattern = Pattern.compile(regex);
        Matcher matcher = pattern.matcher(url);
        List<String> portList  = new ArrayList<>();
        while (matcher.find()) {
            for (int i = 1; i <= 2; i++) {
                if (matcher.group(i) != null) {
                    portList.add(matcher.group(i));
                }
            }
        }
        return portList ;
    }
}
