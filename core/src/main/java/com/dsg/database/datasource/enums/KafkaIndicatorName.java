package com.dsg.database.datasource.enums;

/**
 * @描述
 * @创建人 lf
 * @创建时间 2022/10/13
 * @create 2022-10-13-10:57
 */
public enum KafkaIndicatorName {
    BYTES_IN_PER_SEC("kafka.server:type=BrokerTopicMetrics,name=BytesInPerSec"),
    BYTES_OUT_PER_SEC("kafka.server:type=BrokerTopicMetrics,name=BytesOutPerSec"),
    BYTES_REJECTED_PER_SEC("kafka.server:type=BrokerTopicMetrics,name=BytesRejectedPerSec"),
    FAILED_FETCH_REQUESTS_PER_SEC("kafka.server:type=BrokerTopicMetrics,name=FailedFetchRequestsPerSec"),
    FAILED_PRODUCE_REQUESTS_PER_SEC("kafka.server:type=BrokerTopicMetrics,name=FailedProduceRequestsPerSec"),
    MESSAGES_IN_PER_SEC("kafka.server:type=BrokerTopicMetrics,name=MessagesInPerSec"),
    PRODUCE_MESSAGE_CONVERSIONS_PER_SEC("kafka.server:type=BrokerTopicMetrics,name=ProduceMessageConversionsPerSec"),
    REPLICATION_BYTES_IN_PER_SEC("kafka.server:type=BrokerTopicMetrics,name=ReplicationBytesInPerSec"),
    REPLICATION_BYTES_OUT_PER_SEC("kafka.server:type=BrokerTopicMetrics,name=ReplicationBytesOutPerSec"),
    TOTAL_FETCH_REQUESTS_PER_SEC("kafka.server:type=BrokerTopicMetrics,name=TotalFetchRequestsPerSec"),
    TOTAL_PRODUCE_REQUESTS_PER_SEC("kafka.server:type=BrokerTopicMetrics,name=TotalProduceRequestsPerSec"),
    BYTES_IN_PER_SEC_TOPIC("kafka.server:type=BrokerTopicMetrics,name=BytesInPerSec,topic=%s"),
    BYTES_OUT_PER_SEC_TOPIC("kafka.server:type=BrokerTopicMetrics,name=BytesOutPerSec,topic=%s"),
    JMX_PERFORMANCE_TYPE("java.lang:type=OperatingSystem"),
    TOTAL_PHYSICAL_MEMORY_SIZE("TotalPhysicalMemorySize"),
    FREE_PHYSICAL_MEMORY_SIZE("FreePhysicalMemorySize"),
    PROCESS_CPU_LOAD("ProcessCpuLoad"),
    BROKER_VERSION("kafka.server:type=app-info,id=%s"),
    BROKER_VERSION_VALUE("Version"),
    FIFTEEN_MINUTE_RATE("FifteenMinuteRate"),
    FIVE_MINUTE_RATE("FiveMinuteRate"),
    MEAN_RATE("MeanRate"),
    ONE_MINUTE_RATE("OneMinuteRate"),
    MESSAGES_IN("Messages in"),
    BYTES_IN("Bytes in"),
    BYTES_OUT("Bytes out"),
    BYTES_REJECTED("Bytes rejected"),
    FAILED_FETCH_REQUEST("Failed fetch request"),
    FAILED_PRODUCE_REQUEST("Failed produce request"),
    PRODUCEMESSAGECONVERSIONS("Total fetch requests"),
    TOTALFETCHREQUESTSPERSEC("Total produce requests"),
    TOTALPRODUCEREQUESTSPERSEC("Produce message conversions"),
    OSTOTALMEMORY("os_total_memory"),
    OSFREEMEMORY("os_free_memory"),
    CPUUSED("cpu_used"),
    MESSAGEIN("message_in"),
    BYTEIN("byte_in"),
    BYTEOUT("byte_out"),
    BYTESREJECTED("byte_rejected"),
    FAILEDFETCHREQUEST("failed_fetch_request"),
    FAILEDPRODUCEREQUEST("failed_produce_request"),
    REPLICATIONBYTESINPERSEC("replication_bytes_out"),
    REPLICATIONBYTESOUTPERSEC("replication_bytes_in");

    private String value;

    public String getValue() {
        return value;
    }

    private KafkaIndicatorName(String value) {
        this.value = value;
    }

}
