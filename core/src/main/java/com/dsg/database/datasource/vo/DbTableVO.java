package com.dsg.database.datasource.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * table Info
 *
 * <AUTHOR>
 * date：Created in 下午12:25 2022/3/21
 * company: www.dtstack.com
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DbTableVO {

    /**
     * 库名
     */
    private String dbName;

    /**
     * 模式
     */
    private String schemaName;

    /**
     * 名称
     */
    private String name;

    /**
     * 类型  view-table
     */
    private String type;
    /**
     * 注释
     */
    private String comment;

    /**
     * 字段个数
     */
    private Integer columnCount;

    /**
     * 表名:索引使用、函数使用
     */
    private String tableName;


    /**
     * 类型  索引类型
     */
    private String indexType;

    /**
     * 是否唯一索引 0 是 1 不是
     */
    private Integer unique=1;



    /**
     * ddl
     */
    private String ddl;

    /**
     * 返回类型
     */
    private String returnType;

    /**
     * 创建时间
     */
    private String createTime;

    /**
     * 修改时间
     */
    private String lastAltered;

    /**
     * 状态
     */
    private String status;


    /**
     *排序字符集
     */
    private String collation;

    /**
     *字符集
     */
    private String charset;

    /**
     * 外部语言
     */
    private String externalLanguage;

    /**
     * -----------------------------------------------------------文件类型元数据-----------------------------------------------------------
     */

    /**
     * 文件路径
     */
    private String filePath;
    /**
     * 文件类型
     */
    private String fileType;
    /**
     * 文件大小
     */
    private String fileSize;
    /**
     * 修改时间
     */
    private String fileModifiedTime;
    /**
     * 权限
     */
    private String accessPermission;
    /**
     * 文件所属目录
     */
    private String fileParentPath;
    /**
     * 文件所属用户组
     */
    private String fileGroup;
    /**
     * 文件所属用户
     */
    private String fileOwner;
    /**
     * 文件目录层级
     */
    private String fileLevel;

    /*
    =======================================================kafka=========================
     */

    /**
     * 副本数
     */
    private Integer replicaFactor;



}
