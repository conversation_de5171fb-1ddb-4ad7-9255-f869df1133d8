/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.dtstack.dtcenter.loader.client;

import com.dsg.database.datasource.vo.DbTableVO;
import com.dsg.database.datasource.dto.*;
import com.dtstack.dtcenter.loader.IDownloader;
import com.dtstack.dtcenter.loader.dto.*;
import com.dtstack.dtcenter.loader.dto.source.ISourceDTO;

import java.sql.Connection;
import java.sql.SQLException;
import java.util.List;
import java.util.Map;

/**
 * @company: www.dtstack.com
 * <AUTHOR>
 * @Date ：Created in 09:27 2020/1/13
 * @Description：客户端接口
 */
public interface IClient<T> {
    /**
     * 获取 连接
     *
     * @param source 数据源信息
     * @return
     * @throws Exception
     */
    Connection getCon(ISourceDTO source);

    /**
     * 获取 连接
     *
     * @param source     数据源信息
     * @param taskParams 任务环境变量
     * @return
     * @throws Exception
     */
    Connection getCon(ISourceDTO source, String taskParams);

    /**
     * 获取流式结果集
     *
     * @param source       数据源信息
     * @param queryDTO     数据源信息
     * @param enableStream 是否开启流式查询数据，默认为false
     * @param autoCommit   是否开启自动提交事务，默认为true
     * @return Exception
     */
    ConnectorResultEntity getResultSetStream(ISourceDTO source, SqlQueryDTO queryDTO, boolean enableStream, boolean autoCommit);

    /**
     * 校验 连接
     *
     * @param source
     * @return
     * @throws Exception
     */
    Boolean testCon(ISourceDTO source);

    /**
     * 执行查询
     *
     * @param source
     * @param queryDTO 必填项 sql
     * @return
     * @throws Exception
     */
    List<Map<String, Object>> executeQuery(ISourceDTO source, SqlQueryDTO queryDTO);

    /**
     * 执行查询
     *
     * @param source
     * @param queryDTO 必填项 sql
     * @return
     * @throws Exception
     */
    List<JdbcSqlMetadataInfoDTO> executeSqlForMetadataInfo(ISourceDTO source, SqlQueryDTO queryDTO);

    /**
     * 执行查询，无需结果集
     *
     * @param source
     * @param queryDTO 必填项 sql
     * @return
     * @throws Exception
     */
    Boolean executeSqlWithoutResultSet(ISourceDTO source, SqlQueryDTO queryDTO);

    /**
     * 返回所有的表字段名称
     * 是否视图表，默认不过滤
     *
     * @param source
     * @param queryDTO
     * @return
     * @throws Exception
     */
    List<String> getTableList(ISourceDTO source, SqlQueryDTO queryDTO);

    /**
     * 返回所有的表和视图名称
     *
     * @param source
     * @param queryDTO
     * @return
     * @throws Exception
     */
    List<TableViewDTO> getTableAndViewList(ISourceDTO source, SqlQueryDTO queryDTO);

    /**
     * 返回字符集
     *
     * @param source
     * @param queryDTO
     * @return
     * @throws Exception
     */
    String getCharacterSet(ISourceDTO source, SqlQueryDTO queryDTO);

    /**
     * 返回表的行数
     *
     * @param source
     * @param queryDTO
     * @return
     */
    Long getTableRows(ISourceDTO source, SqlQueryDTO queryDTO);

    /**
     * 返回排序规则
     *
     * @param source
     * @param queryDTO
     * @return
     * @throws Exception
     */
    String getCharacterCollation(ISourceDTO source, SqlQueryDTO queryDTO);

    /**
     * 根据schema/db获取表
     *
     * @param source
     * @param queryDTO
     * @return
     * @throws Exception
     */
    List<String> getTableListBySchema(ISourceDTO source, SqlQueryDTO queryDTO);

    /**
     * 返回字段 Java 类的标准名称
     * 字段名若不填则默认全部
     *
     * @param source
     * @param queryDTO 必填项 表名
     * @return
     * @throws Exception
     */
    List<String> getColumnClassInfo(ISourceDTO source, SqlQueryDTO queryDTO);

    /**
     * 获取表字段属性
     * 字段名若不填则默认全部, 是否过滤分区字段 不填默认不过滤
     *
     * @param source
     * @param queryDTO 必填项 表名
     * @return
     * @throws Exception
     */
    List<ColumnMetaDTO> getColumnMetaData(ISourceDTO source, SqlQueryDTO queryDTO);

    /**
     * 根据自定义sql获取表字段属性
     *
     * @param source
     * @param queryDTO 必填项 sql
     * @return
     * @throws Exception
     */
    List<ColumnMetaDTO> getColumnMetaDataWithSql(ISourceDTO source, SqlQueryDTO queryDTO);

    /**
     * 获取flinkSql字段名称
     *
     * @param source
     * @param queryDTO
     * @return
     * @throws Exception
     */
    List<ColumnMetaDTO> getFlinkColumnMetaData(ISourceDTO source, SqlQueryDTO queryDTO);

    /**
     * 获取表注释
     *
     * @param source
     * @param queryDTO
     * @return
     * @throws Exception
     */
    String getTableMetaComment(ISourceDTO source, SqlQueryDTO queryDTO);

    /**
     * 获取预览数据
     *
     * @param source
     * @param queryDTO
     * @return
     * @throws Exception
     */
    List<List<Object>> getPreview(ISourceDTO source, SqlQueryDTO queryDTO);

    /**
     * 获取预览数据总条数
     *
     * @param source
     * @param queryDTO
     * @return
     */
    int getPreviewRows(ISourceDTO source, SqlQueryDTO queryDTO);

    /**
     * 获取预览数据SQL
     */
    String dealSql(ISourceDTO sourceDTO, SqlQueryDTO sqlQueryDTO);

    /**
     * 获取对应的downloader
     *
     * @param source
     * @param queryDTO
     * @return
     * @throws Exception
     */
    IDownloader getDownloader(ISourceDTO source, SqlQueryDTO queryDTO) throws Exception;

    /**
     * 根据执行 sql 下载数据
     *
     * @param source   数据源信息
     * @param sql      执行 sql
     * @param pageSize 每页的数量
     * @return 数据下载 downloader
     * @throws Exception 异常
     */
    IDownloader getDownloader(ISourceDTO source, String sql, Integer pageSize) throws Exception;

    /**
     * 获取所有的 DB  含模式 得这个方法是获取所有模式
     *
     * @param source
     * @param queryDTO
     * @return
     * @throws Exception
     */
    List<String> getAllDatabases(ISourceDTO source, SqlQueryDTO queryDTO);

    /**
     * 获取所有的 DB(给含有模式得使用)
     *
     * @param source
     * @param queryDTO
     * @return
     * @throws Exception
     */
    List<String> getAllDbs(ISourceDTO source, SqlQueryDTO queryDTO);



    /**
     * 获取所有的db，此方法目的为获取所有的 database，为了解决一些遗留问题。例如 oracle 12 后支持 cdb
     * 模式，此时 oracle 可以包含多个 pdb，每个 pdb 下面可以有多个 schema，但是getAllDatabases 返回
     * 的是 schema 列表
     *
     * @param source   数据源信息
     * @param queryDTO 查询信息
     * @return db 列表
     * @see IClient#getAllDatabases 该方法对于有 database 概念的数据源返回的是 database，否则返回 schema
     */
    List<String> getRootDatabases(ISourceDTO source, SqlQueryDTO queryDTO);

    /**
     * 获取建表语句
     *
     * @param source
     * @param queryDTO
     * @return
     * @throws Exception
     */
    String getCreateTableSql(ISourceDTO source, SqlQueryDTO queryDTO);

    /**
     * 获取分区字段
     *
     * @param source
     * @param queryDTO
     * @return
     * @throws Exception
     */
    List<ColumnMetaDTO> getPartitionColumn(ISourceDTO source, SqlQueryDTO queryDTO);

    /**
     * 获取表信息
     *
     * @param source
     * @param queryDTO
     * @return
     * @throws Exception
     */
    Table getTable(ISourceDTO source, SqlQueryDTO queryDTO);

    /**
     * 获取当前使用的数据库
     *
     * @param source 数据源信息
     * @return 当前使用的数据库
     * @throws Exception
     */
    String getCurrentDatabase(ISourceDTO source);

    /**
     * 获取当前使用的数据库模式
     *
     * @param source 数据源信息
     * @return 当前使用的数据库
     * @throws Exception
     */
    String getCurrentSchema(ISourceDTO source);

    /**
     * 创建数据库
     *
     * @param source  数据源信息
     * @param dbName  需要创建的数据库
     * @param comment 库注释信息
     * @return 创建结果
     * @throws Exception
     */
    Boolean createDatabase(ISourceDTO source, String dbName, String comment);

    /**
     * 判断数据库是否存在
     *
     * @param source 数据源信息
     * @param dbName 数据库名称
     * @return 判断结果
     * @throws Exception
     */
    Boolean isDatabaseExists(ISourceDTO source, String dbName);

    /**
     * 判断该数据库中是否存在该表
     *
     * @param source    数据源信息
//     * @param tableName 表名
//     * @param dbName    库名
     * @return 判断结果
     * @throws Exception
     */
    Boolean isTableExistsInDatabase(ISourceDTO source,  SqlQueryDTO queryDTO);

    /**
     * 获取数据源/数据库目录列表，目前presto使用，后续pgSql等可以实现该方法用于获取所有库
     *
     * @param source
     * @return 数据源目录
     */
    List<String> getCatalogs(ISourceDTO source);

    /**
     * 获取当前数据源的版本
     *
     * @param source 数据源信息
     * @return 数据源版本
     */
    String getVersion(ISourceDTO source);

    /**
     * 列出指定路径下的文件
     *
     * @param sourceDTO  数据源信息
     * @param path       路径
     * @param includeDir 是否包含文件夹
     * @param recursive  是否递归
     * @param maxNum     最大返回条数
     * @param regexStr   正则匹配
     * @return 文件集合
     */
    List<String> listFileNames(ISourceDTO sourceDTO, String path, Boolean includeDir, Boolean recursive, Integer maxNum, String regexStr);

    /**
     * 获取数据库信息
     *
     * @param sourceDTO 数据源信息
     * @param dbName    数据库名称
     * @return 数据库详细信息
     */
    Database getDatabase(ISourceDTO sourceDTO, String dbName);

    /**
     * 获取 table 信息
     *
     * @param sourceDTO 数据源信息
     * @param tableName 表名
     * @return table 信息
     */
    TableInfo getTableInfo(ISourceDTO sourceDTO, String tableName);

    /**
     * 返回数据库的字符集
     *
     * @param source
     * @param queryDTO
     * @return
     * @throws Exception
     */
    String getCharacterSetByDatabase(ISourceDTO source, SqlQueryDTO queryDTO);

    /**
     * 获取时区
     *
     * @param source
     * @param queryDTO
     * @return
     * @throws Exception
     */
    String getTimeZoneByDatabase(ISourceDTO source, SqlQueryDTO queryDTO);


    /**
     * 获取dataType映射
     *
     * @param source
     * @param queryDTO
     * @return
     * @throws Exception
     */
    ColumnMetaDTO getDataType(ISourceDTO source, SqlQueryDTO queryDTO);

    /**
     * 获取数据源动态导入
     *
     * @param source
     * @param queryDTO
     * @return
     * @throws Exception
     */
    DatasourceInfoDTO getDataSourceImport(ISourceDTO source, SqlQueryDTO queryDTO);

    String dealWhereSql(ISourceDTO source, String previewSql, SqlQueryDTO queryDTO);


    /**
     * 获取存储过程
     *
     */
     ProcedureMetadata getProduce(ISourceDTO source, SqlQueryDTO queryDTO);


    /**
     * 获取存储过程参数
     *
     */
     List<ProcedureMetadataArguments> getProduceArguments(ISourceDTO source, SqlQueryDTO queryDTO);
    /**
     *获取表标签
     *
     */
     String getTableLabel(ISourceDTO source, SqlQueryDTO queryDTO) ;

    /**
     * 获取 jdbcurl
     * @param sourceDTO
     * @param queryDTO
     * @return
     */
     String getUrl(ISourceDTO sourceDTO, SqlQueryDTO queryDTO);


    /**
     * 获取数据库表信息列表
     * 本函数根据数据源信息和查询条件，获取特定数据源中的表信息
     * 主要用于元数据管理模块，帮助用户了解和管理数据库表结构
     *
     * @param sourceDTO 数据源描述对象，包含连接数据库所需的信息，如数据库类型、连接字符串等
     * @param queryDTO  SQL查询对象，包含查询数据库表所需的条件，如表名、schema名等
     * @return 返回一个包含数据库表信息的列表每个表信息对象（DbTableVO）包含表的详细信息，
     * 如表名、表描述、列信息等
     */
    List<DbTableVO> getMedataDataTables(ISourceDTO sourceDTO, SqlQueryDTO queryDTO);

    /**
     * 判断表是否存在
     * @param sourceDTO
     * @param queryDTO
     * @return
     */
    List<String> isTablesExists(ISourceDTO sourceDTO, SqlQueryDTO queryDTO);

    /**
     * 获取库 或者表的索引信息
     * @param sourceDTO
     * @param queryDTO
     * @return
     */
    List<DbTableVO> getIndexList(ISourceDTO sourceDTO, SqlQueryDTO queryDTO);

    /**
     * 获取索引字段信息
     * @param sourceDTO
     */
    List<ColumnMetaDTO> getIndexColumn(ISourceDTO sourceDTO, SqlQueryDTO queryDTO);

    /**
     * 获取函数获取存储过程列表
     * @param sourceDTO
     * @param queryDTO
     * @return
     */
    List<DbTableVO> getFunctionList(ISourceDTO sourceDTO, SqlQueryDTO queryDTO);

    /**
     * 获取存储过程参数
     * @param sourceDTO
     * @param queryDTO
     * @return
     */
    List<ColumnMetaDTO> getFunctionArguments(ISourceDTO sourceDTO, SqlQueryDTO queryDTO);

    /**
     * 获取最大连接数
     * @param sourceDTO 数据源信息
     * @return 最大连接数
     */
    Integer getMaxConnections(ISourceDTO sourceDTO);

    /**
     * 获取元数据权限
     * @param sourceDTO 数据源信息
     * @return 元数据权限
     */
    Boolean getMetadataPrivileges(ISourceDTO sourceDTO) throws SQLException;
}
