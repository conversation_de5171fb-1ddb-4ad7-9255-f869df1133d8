/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.dtstack.dtcenter.common.loader.common.service;

import com.dtstack.dtcenter.common.loader.common.exception.IErrorPattern;

/**
 * 数据库错误分析、统一接口
 *
 * <AUTHOR>
 * date：Created in 上午10:53 2020/11/6
 * company: www.dtstack.com
 */
public interface IErrorAdapter {

    /**
     * 获取连接失败分析
     *
     * @param errorMsg 错误信息
     * @param errorPattern 对应的正则实现类
     * @return 统一的错误描述
     */
    String connAdapter(String errorMsg, IErrorPattern errorPattern);

    /**
     * sql执行失败分析
     *
     * @param errorMsg 错误信息
     * @param errorPattern 对应的正则实现类
     * @return 统一的错误描述
     */
    String sqlAdapter(String errorMsg, IErrorPattern errorPattern);
}
