/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.dtstack.dtcenter.common.loader.kingbase;

import com.dsg.database.datasource.vo.DbTableVO;
import com.dtstack.dtcenter.common.loader.common.utils.DBUtil;
import com.dtstack.dtcenter.common.loader.rdbms.AbsRdbmsClient;
import com.dtstack.dtcenter.common.loader.rdbms.ConnFactory;
import com.dtstack.dtcenter.loader.IDownloader;
import com.dtstack.dtcenter.loader.dto.ColumnMetaDTO;
import com.dtstack.dtcenter.loader.dto.SqlQueryDTO;
import com.dtstack.dtcenter.loader.dto.source.ISourceDTO;
import com.dtstack.dtcenter.loader.dto.source.KingbaseSourceDTO;
import com.dtstack.dtcenter.loader.dto.source.RdbmsSourceDTO;
import com.dtstack.dtcenter.loader.exception.DtLoaderException;
import com.dtstack.dtcenter.loader.source.DataSourceType;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;

import java.sql.*;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * company: www.dtstack.com
 *
 * <AUTHOR>
 * Date ：Created in 17:18 2020/09/01
 * Description：kingbase 客户端
 */
@Slf4j
public class KingbaseClient extends AbsRdbmsClient {

    /**
     * 获取所有schema，去除系统库
     */
    private static final String SCHEMA_SQL = "SELECT NSPNAME FROM SYS_CATALOG.SYS_NAMESPACE WHERE NSPNAME !~ 'sys' AND NSPNAME <> 'information_schema' ORDER BY NSPNAME ";

    /**
     * 获取某个schema下的所有表
     */
    private static final String SCHEMA_TABLE_SQL = "SELECT tablename FROM SYS_CATALOG.sys_tables WHERE schemaname = '%s' %s";

    /**
     * 获取所有表名，表名前拼接schema，并对schema和tableName进行增加双引号处理
     */
    private static final String ALL_TABLE_SQL = "SELECT '\"'||schemaname||'\".\"'||tablename||'\"' AS schema_table FROM SYS_CATALOG.sys_tables WHERE 1=1 %s";

    /**
     * 获取某个表的表注释信息
     */
    private static final String TABLE_COMMENT_SQL = "SELECT COMMENTS FROM ALL_TAB_COMMENTS WHERE TABLE_NAME = '%s' ";

    /**
     * 获取某个表的字段注释信息
     */
    private static final String COL_COMMENT_SQL = "SELECT COLUMN_NAME,COMMENTS FROM ALL_COL_COMMENTS WHERE TABLE_NAME = '%s' ";

    /**
     * 获取正在使用数据库
     */
    private static final String CURRENT_DB = "select current_database()";

    /**
     * 获取正在使用 schema
     */
    private static final String CURRENT_SCHEMA = "select current_schema()";

    private static final String DONT_EXIST = "doesn't exist";

    /**
     * 根据schema选表表名模糊查询
     */
    private static final String SEARCH_SQL = " AND tablename LIKE '%s' ";

    /**
     * 限制条数语句
     */
    private static final String LIMIT_SQL = " LIMIT %s ";

    /**
     * 获取当前版本号
     */
    private static final String SHOW_VERSION = "select version()";

    /**
     * 获取数据库字符集
     *
     * @return
     */
    private static final String SHOW_CHARACTER = "SELECT pg_encoding_to_char(encoding) AS character_set FROM pg_database WHERE datname = '%s'";

    private static final String TABLE_ROWS = "select count(1) from %s.\"%s\"";

    private static final String SCHEMA_COLLATION_NAME = "SELECT c.collname AS collation_name\n" +
            "FROM pg_collation c\n" +
            "JOIN pg_namespace n ON c.collnamespace = n.oid\n" +
            "WHERE n.nspname = '%s';";

    @Override
    protected ConnFactory getConnFactory() {
        return new KingbaseConnFactory();
    }

    @Override
    protected DataSourceType getSourceType() {
        return DataSourceType.KINGBASE8;
    }

    @Override
    public List<String> getTableList(ISourceDTO source, SqlQueryDTO queryDTO) {
        return getTableListBySchema(source, queryDTO);
    }

    @Override
    public Long getTableRows(ISourceDTO iSource, SqlQueryDTO queryDTO) {
        KingbaseSourceDTO kingbaseSourceDTO = (KingbaseSourceDTO) iSource;
        Integer clearStatus = beforeColumnQuery(kingbaseSourceDTO, queryDTO);
        Statement statement = null;
        ResultSet resultSet = null;
        long tableRow = 0L;
        try {
            statement = kingbaseSourceDTO.getConnection().createStatement();
            String schema = queryDTO.getSchema();
            if (StringUtils.isEmpty(schema)) {
                schema = super.getCurrentSchema(iSource);
            }
            resultSet = statement.executeQuery(String.format(TABLE_ROWS, schema, queryDTO.getTableName()));
            while (resultSet.next()) {
                tableRow = resultSet.getInt(1);
                return tableRow;
            }
        } catch (Exception e) {
            throw new DtLoaderException(String.format("Get table count exception：%s", e.getMessage()), e);
        } finally {
            DBUtil.closeDBResources(resultSet, statement, DBUtil.clearAfterGetConnection(kingbaseSourceDTO, clearStatus));
        }
        return tableRow;
    }

    /**
     * 获取表注释信息
     *
     * @param source
     * @param queryDTO
     * @return
     * @throws Exception
     */
    @Override
    public String getTableMetaComment(ISourceDTO source, SqlQueryDTO queryDTO) {
        KingbaseSourceDTO kingbaseSourceDTO = (KingbaseSourceDTO) source;
        Integer clearStatus = beforeColumnQuery(kingbaseSourceDTO, queryDTO);
        Statement statement = null;
        ResultSet resultSet = null;

        try {
            statement = kingbaseSourceDTO.getConnection().createStatement();
            resultSet = statement.executeQuery(String.format(TABLE_COMMENT_SQL, queryDTO.getTableName()));
            while (resultSet.next()) {
                return resultSet.getString(1);
            }
        } catch (Exception e) {
            throw new DtLoaderException(String.format("Failed to get the information of table: %s. Please contact DBA to check the database and table information: %s",
                    queryDTO.getTableName(), e.getMessage()), e);
        } finally {
            DBUtil.closeDBResources(resultSet, statement, DBUtil.clearAfterGetConnection(kingbaseSourceDTO, clearStatus));
        }
        return "";
    }

    /**
     * 处理kingbase schema和tableName，适配schema和tableName中有.的情况
     *
     * @param schema
     * @param tableName
     * @return
     */
    @Override
    protected String transferSchemaAndTableName(String schema, String tableName) {
        if (!tableName.startsWith("\"") || !tableName.endsWith("\"")) {
            tableName = String.format("\"%s\"", tableName);
        }
        if (StringUtils.isBlank(schema)) {
            return tableName;
        }
        if (!schema.startsWith("\"") || !schema.endsWith("\"")) {
            schema = String.format("\"%s\"", schema);
        }
        return String.format("%s.%s", schema, tableName);
    }

    /**
     * 获取所有 数据库/schema sql语句
     *
     * @return
     */
    @Override
    protected String getShowDbSql() {
        return SCHEMA_SQL;
    }

    /**
     * 获取所有 数据库 sql语句
     *
     * @return
     */
    @Override
    protected String getDbsSql() {
        return SqlConstants.DATABASE_QUERY;
    }

    /**
     * 获取字段注释
     *
     * @param sourceDTO
     * @param queryDTO
     * @return
     * @throws Exception
     */
    @Override
    protected Map<String, String> getColumnComments(RdbmsSourceDTO sourceDTO, SqlQueryDTO queryDTO) {
        Integer clearStatus = beforeColumnQuery(sourceDTO, queryDTO);
        Statement statement = null;
        ResultSet rs = null;
        Map<String, String> columnComments = new HashMap<>();
        try {
            statement = sourceDTO.getConnection().createStatement();
            rs = statement.executeQuery(String.format(COL_COMMENT_SQL, queryDTO.getTableName()));
            while (rs.next()) {
                String columnName = rs.getString("COLUMN_NAME");
                String columnComment = rs.getString("COMMENTS");
                columnComments.put(columnName, columnComment);
            }

        } catch (Exception e) {
            throw new DtLoaderException(String.format("Failed to get the comment information of the field of the table: %s. Please contact the DBA to check the database and table information.",
                    queryDTO.getTableName()), e);
        } finally {
            DBUtil.closeDBResources(rs, statement, DBUtil.clearAfterGetConnection(sourceDTO, clearStatus));
        }
        return columnComments;
    }

    @Override
    public List<ColumnMetaDTO> getFlinkColumnMetaData(ISourceDTO source, SqlQueryDTO queryDTO) {
        Integer clearStatus = beforeColumnQuery(source, queryDTO);
        KingbaseSourceDTO kingbaseSourceDTO = (KingbaseSourceDTO) source;
        Statement statement = null;
        ResultSet rs = null;
        List<ColumnMetaDTO> columns = new ArrayList<>();
        try {
            statement = kingbaseSourceDTO.getConnection().createStatement();
            String queryColumnSql = "select * from " + transferSchemaAndTableName(kingbaseSourceDTO, queryDTO)
                    + " where 1=2";
            rs = statement.executeQuery(queryColumnSql);
            ResultSetMetaData rsMetaData = rs.getMetaData();
            for (int i = 0, len = rsMetaData.getColumnCount(); i < len; i++) {
                ColumnMetaDTO columnMetaDTO = new ColumnMetaDTO();
                columnMetaDTO.setKey(rsMetaData.getColumnName(i + 1));
                String type = rsMetaData.getColumnTypeName(i + 1);
                int columnType = rsMetaData.getColumnType(i + 1);
                int precision = rsMetaData.getPrecision(i + 1);
                int scale = rsMetaData.getScale(i + 1);
                //kingbase类型转换
                String flinkSqlType = KingbaseAdapter.mapColumnTypeJdbc2Java(columnType, precision, scale);
                if (StringUtils.isNotEmpty(flinkSqlType)) {
                    type = flinkSqlType;
                }
                columnMetaDTO.setType(type);
                // 获取字段精度
                if (columnMetaDTO.getType().equalsIgnoreCase("decimal")
                        || columnMetaDTO.getType().equalsIgnoreCase("float")
                        || columnMetaDTO.getType().equalsIgnoreCase("double")
                        || columnMetaDTO.getType().equalsIgnoreCase("numeric")) {
                    columnMetaDTO.setScale(rsMetaData.getScale(i + 1));
                    columnMetaDTO.setPrecision(rsMetaData.getPrecision(i + 1));
                }
                columns.add(columnMetaDTO);
            }
            return columns;

        } catch (SQLException e) {
            if (e.getMessage().contains(DONT_EXIST)) {
                throw new DtLoaderException(String.format(queryDTO.getTableName() + "table not exist,%s", e.getMessage()), e);
            } else {
                throw new DtLoaderException(String.format("Failed to get meta information for the fields of table :%s. Please contact the DBA to check the database table information.", queryDTO.getTableName()), e);
            }
        } finally {
            DBUtil.closeDBResources(rs, statement, DBUtil.clearAfterGetConnection(kingbaseSourceDTO, clearStatus));
        }

    }

    @Override
    public IDownloader getDownloader(ISourceDTO source, SqlQueryDTO queryDTO) {
        throw new DtLoaderException("Not Support");
    }

    @Override
    public List<ColumnMetaDTO> getPartitionColumn(ISourceDTO source, SqlQueryDTO queryDTO) {
        throw new DtLoaderException("Not Support");
    }

    @Override
    protected String getCurrentDbSql() {
        return CURRENT_DB;
    }

    @Override
    protected String getCurrentSchemaSql() {
        return CURRENT_SCHEMA;
    }

    @Override
    protected String getTableBySchemaSql(ISourceDTO sourceDTO, SqlQueryDTO queryDTO) {
        RdbmsSourceDTO rdbmsSourceDTO = (RdbmsSourceDTO) sourceDTO;
        String schema = StringUtils.isNotBlank(queryDTO.getSchema()) ? queryDTO.getSchema() : rdbmsSourceDTO.getSchema();
        StringBuilder constr = new StringBuilder();
        if (StringUtils.isNotBlank(queryDTO.getTableNamePattern())) {
            constr.append(String.format(SEARCH_SQL, addFuzzySign(queryDTO)));
        }
        if (Objects.nonNull(queryDTO.getLimit())) {
            constr.append(String.format(LIMIT_SQL, queryDTO.getLimit()));
        }
        // 如果不传scheme，默认使用当前连接使用的schema
        if (StringUtils.isBlank(schema)) {
            return String.format(ALL_TABLE_SQL, constr.toString());
        }
        return String.format(SCHEMA_TABLE_SQL, schema, constr.toString());
    }

    @Override
    protected String getVersionSql() {
        return SHOW_VERSION;
    }

    @Override
    public String getCharacterCollation(ISourceDTO source, SqlQueryDTO queryDTO) {
        ResultSet resultSet = null;
        Statement statement = null;
        KingbaseSourceDTO kingbaseSourceDTO = (KingbaseSourceDTO) source;
        Integer clearStatus = beforeColumnQuery(kingbaseSourceDTO, queryDTO);
        try {
            String curDatabase = super.getCurrentDatabase(source);
            statement = source.getConnection().createStatement();
            resultSet = statement.executeQuery(String.format(SHOW_CHARACTER, curDatabase));
            if (resultSet.next()) {
                return resultSet.getString(1);
            }
        } catch (SQLException e) {
            e.printStackTrace();
        } finally {
            DBUtil.closeDBResources(resultSet, statement, DBUtil.clearAfterGetConnection(kingbaseSourceDTO, clearStatus));
        }
        return "";
    }

    @Override
    public String getCharacterSet(ISourceDTO iSource, SqlQueryDTO queryDTO) {
        ResultSet resultSet = null;
        Statement statement = null;
        KingbaseSourceDTO kingbaseSourceDTO = (KingbaseSourceDTO) iSource;
        Integer clearStatus = beforeColumnQuery(kingbaseSourceDTO, queryDTO);
        try {
            String curDatabase = super.getCurrentSchema(iSource);
            statement = iSource.getConnection().createStatement();
            resultSet = statement.executeQuery(String.format(SCHEMA_COLLATION_NAME, curDatabase));
            StringBuilder collationNameBuilder = new StringBuilder();
            while (resultSet.next()) {
                collationNameBuilder.append(resultSet.getString(1)).append(",");
            }
            if (collationNameBuilder.length() > 0) {
                // 删除最后一个分隔符
                collationNameBuilder.deleteCharAt(collationNameBuilder.length() - 1);
            }
            return collationNameBuilder.toString();
        } catch (SQLException e) {
            e.printStackTrace();
        } finally {
            DBUtil.closeDBResources(resultSet, statement, DBUtil.clearAfterGetConnection(kingbaseSourceDTO, clearStatus));
        }
        return "";
    }


    @Override
    public String getCreateTableSql(ISourceDTO source, SqlQueryDTO queryDTO) {
        Integer clearStatus = beforeQuery(source, queryDTO, false);
        KingbaseSourceDTO kingbaseSourceDTO = (KingbaseSourceDTO) source;
        String schema = StringUtils.isNotBlank(queryDTO.getSchema()) ? queryDTO.getSchema() : kingbaseSourceDTO.getSchema();
        if (SqlConstants.FUNCTION_TYPE.equalsIgnoreCase(queryDTO.getType()) || SqlConstants.PROCEDURE_TYPE.equalsIgnoreCase(queryDTO.getType())) {
            String sql = String.format(SqlConstants.CREATE_FUNCTION_SQL, queryDTO.getTableName(), schema);
            log.error("获取函数或者存储过程的ddl:{}", sql);
            Statement statement = null;
            ResultSet rs = null;
            String createTableSql = null;
            try {
                statement = kingbaseSourceDTO.getConnection().createStatement();
                rs = statement.executeQuery(sql);
                int columnSize = rs.getMetaData().getColumnCount();
                while (rs.next()) {
                    createTableSql = rs.getString(columnSize == 1 ? 1 : 2);
                    break;
                }
            } catch (Exception e) {
                throw new DtLoaderException(String.format("failed to get the create table sql：%s", e.getMessage()), e);
            } finally {
                DBUtil.closeDBResources(rs, statement, DBUtil.clearAfterGetConnection(kingbaseSourceDTO, clearStatus));
            }
            return createTableSql;
        }
        List<Map<String, Object>> columnResult;
        List<Map<String, Object>> constraintResult;
        String tableName;
        if (StringUtils.isNotBlank(schema)) {
            try {
                columnResult = executeQuery(kingbaseSourceDTO, SqlQueryDTO.builder().sql(String.format(SqlConstants.SHOW_TABLE_COLUMN_BY_SCHEMA, queryDTO.getTableName(), schema)).build());
                constraintResult = executeQuery(kingbaseSourceDTO, SqlQueryDTO.builder().sql(String.format(SqlConstants.SHOW_TABLE_CONSTRAINT_BY_SCHEMA_NEW, queryDTO.getTableName(), schema)).build());
                tableName = String.format(SqlConstants.SCHEMA_TABLE_FORMAT, schema, queryDTO.getTableName());
            } finally {
                DBUtil.closeDBResources(null, null, DBUtil.clearAfterGetConnection(kingbaseSourceDTO, clearStatus));
            }
        } else {
            try {
                columnResult = executeQuery(kingbaseSourceDTO, SqlQueryDTO.builder().sql(String.format(SqlConstants.SHOW_TABLE_COLUMN, queryDTO.getTableName())).build());
                constraintResult = executeQuery(kingbaseSourceDTO, SqlQueryDTO.builder().sql(String.format(SqlConstants.SHOW_TABLE_CONSTRAINT, queryDTO.getTableName())).build());
                tableName = queryDTO.getTableName();
            } finally {
                DBUtil.closeDBResources(null, null, DBUtil.clearAfterGetConnection(kingbaseSourceDTO, clearStatus));
            }
        }
        if (CollectionUtils.isEmpty(columnResult) || StringUtils.isBlank(MapUtils.getString(columnResult.get(0), "column"))) {
            throw new DtLoaderException(String.format("Failed to get table %s field", queryDTO.getTableName()));
        }
        String columnStr = MapUtils.getString(columnResult.get(0), "column");
        String constraint = null;
        if (CollectionUtils.isNotEmpty(constraintResult)) {
            constraint = MapUtils.getString(constraintResult.get(0), "constraint");
        }
        if (StringUtils.isNotBlank(constraint)) {
            return String.format(SqlConstants.CREATE_TABLE_TEMPLATE, tableName, columnStr + " , " + constraint);
        }


        return String.format(SqlConstants.CREATE_TABLE_TEMPLATE, tableName, columnStr);
    }

    @Override
    public Integer getMaxConnections(ISourceDTO sourceDTO) {
        Integer result = null;
        try {
            List<Map<String, Object>> resultList = executeQuery(sourceDTO, SqlQueryDTO.builder().sql(SqlConstants.GET_MAX_CONNECTIONS).build());
            if (CollectionUtils.isNotEmpty(resultList)) {
                result = MapUtils.getInteger(resultList.get(0), "setting");
            }
        } catch (Exception e) {
            log.error("get max connections error", e);
        }
        return result;
    }

    @Override
    public Boolean getMetadataPrivileges(ISourceDTO sourceDTO) {
        Boolean result = null;
        KingbaseSourceDTO kingbaseSourceDTO = (KingbaseSourceDTO) sourceDTO;
        Integer clearStatus = beforeQuery(sourceDTO, SqlQueryDTO.builder().build(), false);
        try {
            return queryAccess(kingbaseSourceDTO.getConnection(), SqlConstants.GET_SUPER_USER) || queryAccess(kingbaseSourceDTO.getConnection(), SqlConstants.GET_PG_CATALOG_USAGE) || testMetadataQuery(kingbaseSourceDTO.getConnection());
        } catch (Exception e) {
            // 异常为无权限
            result = false;
        } finally {
            DBUtil.closeDBResources(null, null, DBUtil.clearAfterGetConnection(kingbaseSourceDTO, clearStatus));
        }
        return result;
    }

    private boolean queryAccess(Connection conn, String sql) throws SQLException {
        try (Statement stmt = conn.createStatement();
             ResultSet rs = stmt.executeQuery(sql)) {
            return rs.next() && rs.getBoolean(1);
        }
    }

    private boolean testMetadataQuery(Connection conn) {
        try (Statement stmt = conn.createStatement();
             ResultSet rs = stmt.executeQuery(SqlConstants.GET_PG_CATALOG_TABLE_EXISTS)) {
            return rs.next(); // 查询成功则返回 true
        } catch (SQLException e) {
            return false; // 权限不足时抛出异常
        }
    }

    /**
     * 获取表
     *
     * @param sourceDTO 数据源描述对象，包含连接数据库所需的信息，如数据库类型、连接字符串等
     * @param queryDTO  SQL查询对象，包含查询数据库表所需的条件，如表名、schema名等
     * @return
     */
    @Override
    public List<DbTableVO> getMedataDataTables(ISourceDTO sourceDTO, SqlQueryDTO queryDTO) {
        KingbaseSourceDTO kingbaseSourceDTO = (KingbaseSourceDTO) sourceDTO;
        Integer clearStatus = beforeQuery(sourceDTO, queryDTO, false);
        Statement statement = null;
        ResultSet resultSet = null;
        List<DbTableVO> dbTableVOS = new ArrayList<>();
        try {
            statement = kingbaseSourceDTO.getConnection().createStatement();

            //判断搜查类型为空时查全部
            String sql = "";
            String type = SqlConstants.BASE_TABLE;
            if (SqlConstants.VIEW_TYPE.equalsIgnoreCase(queryDTO.getType())) {
                type = SqlConstants.VIEW;
            }
            sql = String.format(SqlConstants.GET_TABLE_SCHEMA_SQL, queryDTO.getSchema(), type, "");
            if (StringUtils.isNotEmpty(queryDTO.getTableNamePattern())) {
                String format = String.format(SqlConstants.SEARCH_SQL, queryDTO.getTableNamePattern());
                sql = sql + format;
            }
            resultSet = statement.executeQuery(sql);
            while (resultSet.next()) {
                DbTableVO dbTableVO = new DbTableVO();
                dbTableVO.setDbName(queryDTO.getDbName());
                dbTableVO.setSchemaName(queryDTO.getSchema());
                dbTableVO.setName(resultSet.getString("TABLE_NAME"));
                String tableType = resultSet.getString("table_type");
                if ("BASE TABLE".equalsIgnoreCase(tableType)) {
                    tableType = SqlConstants.TABLE_TYPE;
                }
                dbTableVO.setType(tableType);
                dbTableVOS.add(dbTableVO);
            }
        } catch (Exception e) {
            throw new DtLoaderException(String.format("根据指定库获取表或者视图异常:{}"), e);
        } finally {
            DBUtil.closeDBResources(resultSet, statement, DBUtil.clearAfterGetConnection(kingbaseSourceDTO, clearStatus));
        }
        return dbTableVOS;
    }

    /**
     * 获取索引信息
     *
     * @param sourceDTO
     * @param queryDTO
     * @return
     */
    @Override
    public List<DbTableVO> getIndexList(ISourceDTO sourceDTO, SqlQueryDTO queryDTO) {
        KingbaseSourceDTO kingbaseSourceDTO = (KingbaseSourceDTO) sourceDTO;
        Integer clearStatus = beforeQuery(sourceDTO, queryDTO, false);
        Statement statement = null;
        ResultSet resultSet = null;
        List<DbTableVO> indexMetaDTOS = new ArrayList<>();
        try {
            statement = kingbaseSourceDTO.getConnection().createStatement();
            //判断搜查类型为空时查全部
            String sql = "";
            //表名为空时  查所有
            if (StringUtils.isEmpty(queryDTO.getTableName())) {
                sql = String.format(SqlConstants.GET_INDEX_SQL, queryDTO.getSchema());
            } else {
                sql = String.format(SqlConstants.GET_INDEX_SQL_BY_TABLE, queryDTO.getSchema(), queryDTO.getTableName());
            }
            //是否有模糊搜索
            if (StringUtils.isNotEmpty(queryDTO.getTableNamePattern())) {
                String format = String.format(SqlConstants.SEARCH_INDEX_SQL, queryDTO.getTableNamePattern());
                sql = sql + format;
            }
            log.info("getIndexList SQl:" + sql);
            resultSet = statement.executeQuery(sql);
            // 正则表达式解析索引信息
            String regex = "(?i)CREATE\\s+(UNIQUE\\s+)?INDEX\\s+(\\S+)\\s+ON\\s+(\\S+)\\.(\\S+)\\s+USING\\s+(\\S+)\\s*\\(([^)]+)\\)";
            Pattern pattern = Pattern.compile(regex);
            while (resultSet.next()) {
                DbTableVO indexMetaDTO = new DbTableVO();
                indexMetaDTO.setDbName(queryDTO.getDbName());
                indexMetaDTO.setSchemaName(queryDTO.getSchema());
                indexMetaDTO.setTableName(resultSet.getString("TABLE_NAME"));
                indexMetaDTO.setName(resultSet.getString("INDEX_NAME"));
                //获取ddl
                String indexDefinition = resultSet.getString("index_definition");
                indexMetaDTO.setDdl(indexDefinition);
                //解析ddl获取索引是否唯一、索引类型
                if (StringUtils.isNotEmpty(indexDefinition)) {
                    try {
                        Matcher matcher = pattern.matcher(indexDefinition);
                        if (matcher.find()) {
                            boolean isUnique = matcher.group(1) != null; // 是否唯一索引
                            indexMetaDTO.setUnique(isUnique ? 0 : 1);
                            String indexType = matcher.group(5);  // 索引类型 (btree, hash, gist, gin)
                            indexMetaDTO.setIndexType(indexType);
                        }
                    } catch (Exception e) {
                        log.error("解析索引信息异常{}", e.getMessage());
                    }
                }
                indexMetaDTOS.add(indexMetaDTO);
            }
        } catch (Exception e) {
            throw new DtLoaderException(String.format("根据指定库获取索引异常:{}"), e);
        } finally {
            DBUtil.closeDBResources(resultSet, statement, DBUtil.clearAfterGetConnection(kingbaseSourceDTO, clearStatus));
        }
        return indexMetaDTOS;
    }


    @Override
    public List<ColumnMetaDTO> getIndexColumn(ISourceDTO sourceDTO, SqlQueryDTO queryDTO) {
        KingbaseSourceDTO kingbaseSourceDTO = (KingbaseSourceDTO) sourceDTO;
        Integer clearStatus = beforeQuery(sourceDTO, queryDTO, false);
        Statement statement = null;
        ResultSet resultSet = null;
        List<ColumnMetaDTO> indexCols = new ArrayList<>();
        try {
            statement = kingbaseSourceDTO.getConnection().createStatement();
            //判断搜查类型为空时查全部
            String sql = String.format(SqlConstants.GET_INDEX_COLUMN_SQL, queryDTO.getSchema(), queryDTO.getTableName(), queryDTO.getIndexName());
            log.info("getIndexColumn SQl:" + sql);
            resultSet = statement.executeQuery(sql);
            while (resultSet.next()) {
                ColumnMetaDTO indexMetaDTO = new ColumnMetaDTO();
                indexMetaDTO.setKey(resultSet.getString("column_name"));
                indexMetaDTO.setColumnOrder(resultSet.getInt("seq_in_index"));
                indexMetaDTO.setIndexType(resultSet.getString("index_type"));
                //是否为空
                String isNullable = resultSet.getString("is_nullable");
                if ("[v]".equalsIgnoreCase(isNullable)) {
                    indexMetaDTO.setNotNullFlag(true);
                } else {
                    indexMetaDTO.setNotNullFlag(false);
                }
                //是否唯一
                String isUnique = resultSet.getString("is_unique");
                if ("[v]".equalsIgnoreCase(isUnique)) {
                    indexMetaDTO.setUniqueFlag(true);
                } else {
                    indexMetaDTO.setUniqueFlag(false);
                }
                indexCols.add(indexMetaDTO);
            }
        } catch (Exception e) {
            throw new DtLoaderException(String.format("根据指定库获取索引字段异常:{}"), e);
        } finally {
            DBUtil.closeDBResources(resultSet, statement, DBUtil.clearAfterGetConnection(kingbaseSourceDTO, clearStatus));
        }
        return indexCols;
    }

    @Override
    public List<DbTableVO> getFunctionList(ISourceDTO sourceDTO, SqlQueryDTO queryDTO) {
        KingbaseSourceDTO kingbaseSourceDTO = (KingbaseSourceDTO) sourceDTO;
        Integer clearStatus = beforeQuery(sourceDTO, queryDTO, false);
        Statement statement = null;
        ResultSet resultSet = null;
        List<DbTableVO> indexMetaDTOS = new ArrayList<>();
        try {
            statement = kingbaseSourceDTO.getConnection().createStatement();
            //判断搜查类型为空时查全部
            String sql = "";
            //type 为函数还是存储过程
            if (SqlConstants.FUNCTION_TYPE.equalsIgnoreCase(queryDTO.getType())) {
                sql = String.format(SqlConstants.GET_PRODUCE_SQL, queryDTO.getSchema(), SqlConstants.FUNCTION_TYPE);
            }

            if (SqlConstants.PROCEDURE_TYPE.equalsIgnoreCase(queryDTO.getType())) {
                sql = String.format(SqlConstants.GET_PRODUCE_SQL, queryDTO.getSchema(), SqlConstants.PROCEDURE_TYPE);
            }
            if (StringUtils.isNotEmpty(queryDTO.getTableNamePattern())) {
                String format = String.format(SqlConstants.SEARCH_PRODUCE_SQL, queryDTO.getTableNamePattern());
                sql = sql + format;
            }

            log.info("getFunctionList SQl:" + sql);
            resultSet = statement.executeQuery(sql);
            while (resultSet.next()) {
                DbTableVO indexMetaDTO = new DbTableVO();
                indexMetaDTO.setDbName(queryDTO.getDbName());
                indexMetaDTO.setSchemaName(queryDTO.getSchema());
                indexMetaDTO.setName(resultSet.getString("ROUTINE_NAME"));
                String indexType = resultSet.getString("ROUTINE_TYPE");
                indexMetaDTO.setType(indexType);
                indexMetaDTO.setDdl(resultSet.getString("ROUTINE_DEFINITION"));
                indexMetaDTOS.add(indexMetaDTO);
            }
        } catch (Exception e) {
            throw new DtLoaderException(String.format("根据指定库获取函数或者存储过程异常:{}"), e);
        } finally {
            DBUtil.closeDBResources(resultSet, statement, DBUtil.clearAfterGetConnection(kingbaseSourceDTO, clearStatus));
        }
        return indexMetaDTOS;
    }

    @Override
    public List<ColumnMetaDTO> getFunctionArguments(ISourceDTO sourceDTO, SqlQueryDTO queryDTO) {
        KingbaseSourceDTO kingbaseSourceDTO = (KingbaseSourceDTO) sourceDTO;
        Integer clearStatus = beforeQuery(sourceDTO, queryDTO, false);
        Statement statement = null;
        ResultSet resultSet = null;
        List<ColumnMetaDTO> indexCols = new ArrayList<>();
        try {
            statement = kingbaseSourceDTO.getConnection().createStatement();
            //判断搜查类型为空时查全部
            String sql = String.format(SqlConstants.GET_PRODUCE_ARGUMENTS_SQL, queryDTO.getSchema(), queryDTO.getObjectName()).toLowerCase();
            log.info("getFunctionArguments SQl:" + sql);
            resultSet = statement.executeQuery(sql);
            while (resultSet.next()) {
                ColumnMetaDTO indexMetaDTO = new ColumnMetaDTO();
                String parameterName = resultSet.getString("parameter_name");
                indexMetaDTO.setKey(parameterName);
                indexMetaDTO.setType(resultSet.getString("data_type"));
                String parameterMode = resultSet.getString("parameter_mode");
                indexMetaDTO.setInOut(parameterMode);
                indexMetaDTO.setColumnOrder(resultSet.getInt("ordinal_position"));
                int characterOctetLength = resultSet.getInt("character_maximum_length");
                indexMetaDTO.setLength(characterOctetLength);
                indexCols.add(indexMetaDTO);
            }
        } catch (Exception e) {
            throw new DtLoaderException(String.format("根据指定库获取函数或者存储过程异常:{}"), e);
        } finally {
            DBUtil.closeDBResources(resultSet, statement, DBUtil.clearAfterGetConnection(kingbaseSourceDTO, clearStatus));
        }
        return indexCols;
    }
    @Override
    public String getTableExistSql(ISourceDTO source,SqlQueryDTO queryDTO) {
        String sql = String.format(SqlConstants.TABLE_BY_SCHEMA, queryDTO.getSchema(), queryDTO.getTableName());
        return sql;
    }

    @Override
    public String getTimeZoneByDatabase(ISourceDTO source, SqlQueryDTO queryDTO) {
        KingbaseSourceDTO kingbaseSourceDTO = (KingbaseSourceDTO) source;
        Integer clearStatus = beforeQuery(kingbaseSourceDTO, queryDTO, false);
        Statement statement = null;
        ResultSet resultSet = null;
        try {
            statement = kingbaseSourceDTO.getConnection().createStatement();
            resultSet = statement.executeQuery("SHOW timezone");
            while (resultSet.next()) {
                return resultSet.getString(1);
            }
        } catch (Exception e) {
            throw new DtLoaderException(String.format("get time_zone information error. Please contact the DBA to check the database information."), e);
        } finally {
            DBUtil.closeDBResources(resultSet, statement, DBUtil.clearAfterGetConnection(kingbaseSourceDTO, clearStatus));
        }
        return "";
    }

}
