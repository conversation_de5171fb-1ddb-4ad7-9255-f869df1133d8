package com.dtstack.dtcenter.common.loader.kingbase;

public class SqlConstants {

    //函数常量
    public static final String FUNCTION_TYPE = "FUNCTION";
    //存储过程
    public static final String  PROCEDURE_TYPE = "PROCEDURE";
    //表
    public static final String TABLE_TYPE = "TABLE";
    //视图
    public static final String VIEW_TYPE = "VIEW";
    // 视图
    public static final String VIEW = "'VIEW'";

    // 普通表
    public static final String BASE_TABLE = "'BASE TABLE'";

    // 获取指定数据库下的表
    public static final String GET_TABLE_SCHEMA_SQL = "SELECT \n" +
            "    t.table_name, \n" +
            "    t.table_type \n" +
            "FROM information_schema.tables t\n" +
            "WHERE t.table_schema = '%s' \n" +
            "AND t.table_type = %s %s";

    public static final String TABLE_BY_SCHEMA = "SELECT \n" +
            "    t.table_name \n" +
            "FROM information_schema.tables t\n" +
            "WHERE t.table_schema = '%s' \n" +
            "AND t.table_name = '%s' ";

    //表名模糊搜索sql
    // 表名正则匹配模糊查询
    public static final String SEARCH_SQL = " AND t.table_name LIKE  '%%%s%%'  ";


    //获取指定库下或者指定表下的索引
    public static final String GET_INDEX_SQL = "SELECT \n" +
            "    schemaname AS table_schema,\n" +
            "    indexname AS index_name,\n" +
            "    tablename AS table_name,\n" +
            "    indexdef AS index_definition\n" +
            "FROM pg_indexes\n" +
            "WHERE schemaname = '%s' \n";

    /**
     * 索引搜索sql
     */
    public static final String SEARCH_INDEX_SQL = " AND index_name LIKE  '%%%s%%'  ";

    /**
     * 索引分组sql、
     */
    public static final String GROUP_INDEX_SQL = " GROUP BY TABLE_SCHEMA,TABLE_NAME,INDEX_NAME,NON_UNIQUE,INDEX_TYPE,INDEX_COMMENT";





    //获取指定库下指定表的索引
    public static final String GET_INDEX_SQL_BY_TABLE = "";

    //获取索引字段列sql
    public static final String GET_INDEX_COLUMN_SQL = "SELECT \n" +
            "    ix.relname AS index_name,           \n" +
            "    t.relname AS table_name,          \n" +
            "    a.attname AS column_name,          \n" +
            "    array_position(i.indkey, a.attnum) AS seq_in_index,\n" +
            "    am.amname AS index_type,            \n" +
            "    NOT a.attnotnull AS is_nullable,   \n" +
            "    i.indisunique AS is_unique         \n" +
            "FROM pg_class t\n" +
            "JOIN pg_index i ON t.oid = i.indrelid\n" +
            "JOIN pg_class ix ON i.indexrelid = ix.oid\n" +
            "JOIN pg_attribute a ON a.attrelid = t.oid AND a.attnum = ANY(i.indkey)  \n" +
            "JOIN pg_namespace ns ON ns.oid = t.relnamespace  \n" +
            "JOIN pg_am am ON ix.relam = am.oid  \n" +
            "WHERE ns.nspname = '%s'  \n" +
            "AND t.relname = '%s' \n" +
            "AND ix.relname = '%s'" ;


    //获取指定库得函数或者存储过程
    public static final String GET_PRODUCE_SQL = "SELECT \n" +
            "    ROUTINE_SCHEMA, \n" +
            "    ROUTINE_NAME, \n" +
            "    ROUTINE_TYPE, \n" +
            "    ROUTINE_DEFINITION\n" +
            "FROM INFORMATION_SCHEMA.ROUTINES\n" +
            "WHERE ROUTINE_SCHEMA = '%s' AND ROUTINE_TYPE = '%s'" ;

    public static final String SEARCH_PRODUCE_SQL = " AND ROUTINE_NAME LIKE  '%%%s%%'  ";

    //获取指定库得函数或者存储过程参数
    public static final String GET_PRODUCE_ARGUMENTS_SQL = "SELECT\n" +
            "    a.specific_name AS full_name,\n" +
            "    regexp_replace(a.specific_name, E'_[0-9]+$', '') AS function_name,\n" +
            "    a.parameter_name,\n" +
            "    a.data_type,\n" +
            "    a.ordinal_position,\n" +
            "    a.character_maximum_length,\n" +
            "    a.parameter_mode\n" +
            "FROM information_schema.parameters a\n" +
            "WHERE a.parameter_name IS NOT NULL\n" +
            "  AND a.specific_schema = '%s'\n" +
            "  AND regexp_replace(a.specific_name, E'_[0-9]+$', '') = '%s';";


    public static final String DATABASE_QUERY = "SELECT datname FROM pg_database WHERE datistemplate = false";

    /**
     * 查询创建函数语句
     */
    public static final String CREATE_FUNCTION_SQL="SELECT\n" +
            "    pg_get_functiondef(p.oid) AS ddl\n" +
            "FROM\n" +
            "    pg_proc p\n" +
            "JOIN\n" +
            "    pg_namespace n ON p.pronamespace = n.oid\n" +
            "WHERE\n" +
            "    p.proname = '%s'\n" +
            "    AND n.nspname = '%s'";

    public static final String SHOW_TABLE_CONSTRAINT_BY_SCHEMA_NEW =
            "WITH constraint_info AS (\n" +
                    "    SELECT\n" +
                    "        c.conname AS constraint_name,\n" +
                    "        c.contype AS constraint_type,\n" +
                    "        CASE\n" +
                    "            WHEN c.contype = 'p' THEN 'PRIMARY KEY (' || (\n" +
                    "                SELECT array_to_string(array_agg(a.attname), ',')\n" +
                    "                FROM unnest(c.conkey) AS k\n" +
                    "                JOIN pg_attribute a ON a.attrelid = c.conrelid AND a.attnum = k\n" +
                    "            ) || ')'\n" +
                    "            WHEN c.contype = 'u' THEN 'UNIQUE (' || (\n" +
                    "                SELECT array_to_string(array_agg(a.attname), ',')\n" +
                    "                FROM unnest(c.conkey) AS k\n" +
                    "                JOIN pg_attribute a ON a.attrelid = c.conrelid AND a.attnum = k\n" +
                    "            ) || ')'\n" +
                    "            WHEN c.contype = 'f' THEN 'FOREIGN KEY (' || (\n" +
                    "                SELECT array_to_string(array_agg(a.attname), ',')\n" +
                    "                FROM unnest(c.conkey) AS k\n" +
                    "                JOIN pg_attribute a ON a.attrelid = c.conrelid AND a.attnum = k\n" +
                    "            ) || ') REFERENCES ' || (\n" +
                    "                SELECT relname FROM pg_class WHERE oid = c.confrelid\n" +
                    "            ) || '(' || (\n" +
                    "                SELECT array_to_string(array_agg(a.attname), ',')\n" +
                    "                FROM unnest(c.confkey) AS k\n" +
                    "                JOIN pg_attribute a ON a.attrelid = c.confrelid AND a.attnum = k\n" +
                    "            ) || ')'\n" +
                    "            WHEN c.contype = 'c' THEN 'CHECK (' || pg_get_constraintdef(c.oid) || ')'\n" +
                    "        END AS constraint_definition\n" +
                    "    FROM\n" +
                    "        pg_constraint c\n" +
                    "    JOIN\n" +
                    "        pg_class cls ON c.conrelid = cls.oid\n" +
                    "    JOIN\n" +
                    "        pg_namespace nsp ON cls.relnamespace = nsp.oid\n" +
                    "    WHERE\n" +
                    "        nsp.nspname = '%2$s'  -- Schema 名称\n" +
                    "        AND cls.relname = '%1$s'  -- 表名称\n" +
                    "        AND c.contype IN ('p', 'u', 'f', 'c')  -- 约束类型\n" +
                    ")\n" +
                    "SELECT\n" +
                    "    array_to_string(array_agg('CONSTRAINT ' || constraint_name || ' ' || constraint_definition), ', ') AS constraints\n" +
                    "FROM\n" +
                    "    constraint_info;";


    /**
     * 获取最大连接数
     */
    public static final String GET_MAX_CONNECTIONS = "SELECT setting FROM pg_settings WHERE name = 'max_connections'";

    /**
     * 判断当前用户是否是超级用户
     */
    public static final String GET_SUPER_USER = "SELECT rolsuper FROM pg_roles WHERE rolname = CURRENT_USER";

    /**
     * 判断当前用户是否拥有pg_catalog.USAGE权限
     */
    public static final String GET_PG_CATALOG_USAGE = "SELECT has_schema_privilege(CURRENT_USER, 'pg_catalog', 'USAGE')";

    /**
     * 判断pg_catalog.pg_tables表是否存在
     */
    public static final String GET_PG_CATALOG_TABLE_EXISTS = "SELECT tablename FROM pg_catalog.pg_tables LIMIT 1";


    // 获取指定 schema 下面表的字段信息
    public static final String SHOW_TABLE_COLUMN_BY_SCHEMA =
            "SELECT array_to_string(ARRAY(\n" +
                    "    SELECT \n" +
                    "        column_name || ' ' || data_type || \n" +
                    "        COALESCE('(' || character_maximum_length || ')', '') || \n" +
                    "        CASE WHEN is_nullable = 'NO' THEN ' NOT NULL' ELSE '' END || \n" +
                    "        COALESCE(' DEFAULT ' || column_default, '')\n" +
                    "    FROM information_schema.columns\n" +
                    "    WHERE table_name = '%1$s' AND table_schema = '%2$s'\n" +
                    "    ORDER BY ordinal_position\n" +
                    "), ',') AS column";


    // 格式刷 schema 和 表名
    public static final String SCHEMA_TABLE_FORMAT = "\"%s\".\"%s\"";


    // 获取指定表的字段信息
    public static final String SHOW_TABLE_COLUMN = "SELECT array_to_string(ARRAY(select concat( c1, c2, c3, c4) as column_line from (select column_name || ' ' || data_type as c1,case when character_maximum_length > 0 then '(' || character_maximum_length || ')' end as c2,case when is_nullable = 'NO' then ' NOT NULL' end as c3,case when column_default is not Null then ' DEFAULT' end || ' ' || replace(column_default, '::character varying', '') as c4 from information_schema.columns where table_name = '%1$s' order by ordinal_position) as string_columns), ',') as column";
    // 获取指定 schema 下面表的约束
    public static final String SHOW_TABLE_CONSTRAINT = "select array_to_string(\n" +
            "array(\n" +
            "select concat(' CONSTRAINT ',conname ,c,u,p,f)   from (\n" +
            "select conname,\n" +
            "case when contype='c' then ' CHECK('|| consrc ||')' end  as c  ,\n" +
            "case when contype='u' then ' UNIQUE('|| ( SELECT array_to_string(ARRAY (SELECT A.attname FROM pg_attribute A WHERE A.attrelid = (select oid from pg_class where relname= '%1$s' ) AND A.attnum IN ( SELECT UNNEST ( conkey ) FROM pg_constraint C WHERE contype = 'u' AND C.conrelid = ((select oid from pg_class where relname= '%1$s' )) AND ( array_to_string( conkey, ',' ) IS NOT NULL))),',') ) ||')' end as u ,\n" +
            "case when contype='p' then ' PRIMARY KEY ('|| ( SELECT array_to_string(ARRAY (SELECT A.attname FROM pg_attribute A WHERE A.attrelid = (select oid from pg_class where relname= '%1$s' ) AND A.attnum IN ( SELECT UNNEST ( conkey ) FROM pg_constraint C WHERE contype = 'p' AND C.conrelid = ((select oid from pg_class where relname= '%1$s' )) AND ( array_to_string( conkey, ',' ) IS NOT NULL))),',')) ||')' end  as p  ,\n" +
            "case when contype='f' then ' FOREIGN KEY('|| ( SELECT array_to_string(ARRAY (SELECT A.attname FROM pg_attribute A WHERE A.attrelid = (select oid from pg_class where relname= '%1$s' ) AND A.attnum IN ( SELECT UNNEST ( conkey ) FROM pg_constraint C WHERE contype = 'u' AND C.conrelid = ((select oid from pg_class where relname= '%1$s' )) AND ( array_to_string( conkey, ',' ) IS NOT NULL))),',')) ||') REFERENCES '|| \n" +
            "(select p.relname from pg_class p where p.oid=c.confrelid )  || '('|| ( SELECT array_to_string(ARRAY (SELECT A.attname FROM pg_attribute A WHERE A.attrelid = ((select oid from pg_class where relname= '%1$s' )) AND A.attnum IN ( SELECT UNNEST ( conkey ) FROM pg_constraint C WHERE contype = 'u' AND C.conrelid = (select oid from pg_class where relname= '%1$s' ) AND ( array_to_string( conkey, ',' ) IS NOT NULL))),',') ) ||')' end as  f\n" +
            "from pg_constraint c\n" +
            "where contype in('u','c','f','p') and conrelid=( \n" +
            "select oid  from pg_class  where relname='%1$s' \n" +
            " )\n" +
            ") as t  \n" +
            ") ,',' ) as constraint";


    // 建表模版
    public static final String CREATE_TABLE_TEMPLATE = "CREATE TABLE %s (%s);";

}
