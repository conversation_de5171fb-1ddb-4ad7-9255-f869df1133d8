/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.dtstack.dtcenter.loader.client.sql;

import com.dtstack.dtcenter.loader.IDownloader;
import com.dtstack.dtcenter.loader.client.BaseTest;
import com.dtstack.dtcenter.loader.client.ClientCache;
import com.dtstack.dtcenter.loader.client.IClient;
import com.dtstack.dtcenter.loader.dto.ColumnMetaDTO;
import com.dtstack.dtcenter.loader.dto.SqlQueryDTO;
import com.dtstack.dtcenter.loader.dto.Table;
import com.dtstack.dtcenter.loader.dto.source.ArgodbSourceDTO;
import com.dtstack.dtcenter.loader.source.DataSourceType;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.junit.Assert;
import org.junit.Test;

import java.sql.Connection;
import java.util.*;


@Slf4j
public class ArgodbTest extends BaseTest {

    /**
     * 构造 ARGODB 客户端
     */
    private static final IClient ARGODB_CLIENT = ClientCache.getClient(DataSourceType.ArgoDB.getVal());

    /**
     * 构建数据源信息
     */
    private static final ArgodbSourceDTO ARGODB_SOURCE_DTO = ArgodbSourceDTO.builder()
            .url("************************")
            .username("hive")
            .password("123456")
            .build();

    @Test
    public void testCon() {
        Assert.assertTrue(ARGODB_CLIENT.testCon(ARGODB_SOURCE_DTO));
    }

    /**
     * 获取连接测试
     */
    @Test
    public void getCon() throws Exception {
        Connection con = ARGODB_CLIENT.getCon(ARGODB_SOURCE_DTO);
        Assert.assertNotNull(con);
        con.close();
    }

    @Test
    public void executeQuery() {
        SqlQueryDTO queryDTO = SqlQueryDTO.builder().sql("show tables").build();
        List<Map<String, Object>> mapList = ARGODB_CLIENT.executeQuery(ARGODB_SOURCE_DTO, queryDTO);
        Assert.assertTrue(CollectionUtils.isNotEmpty(mapList));
    }

    @Test
    public void executeSqlWithoutResultSet() {
        SqlQueryDTO queryDTO = SqlQueryDTO.builder().sql("show tables").build();
        ARGODB_CLIENT.executeSqlWithoutResultSet(ARGODB_SOURCE_DTO, queryDTO);
    }

    @Test
    public void getPreview() {
        SqlQueryDTO queryDTO = SqlQueryDTO.builder().tableName("loader_test_text").build();
        List preview = ARGODB_CLIENT.getPreview(ARGODB_SOURCE_DTO, queryDTO);
        Assert.assertTrue(CollectionUtils.isNotEmpty(preview));
    }

    @Test
    public void getDownloader() throws Exception {
        System.setProperty("HADOOP_USER_NAME", "hive");
        IClient client = ClientCache.getClient(DataSourceType.ArgoDB.getVal());
        long start = System.currentTimeMillis();
        SqlQueryDTO queryDTO = SqlQueryDTO.builder().sql("select * from tag_test_text").build();
        IDownloader downloader = client.getDownloader(ARGODB_SOURCE_DTO, queryDTO);
        Assert.assertTrue(CollectionUtils.isNotEmpty(downloader.getMetaInfo()));
        while (!downloader.reachedEnd()) {
            System.out.println(downloader.readNext());
        }
    }

    @Test
    public void getDownloader_orc() throws Exception {
        IClient client = ClientCache.getClient(DataSourceType.ArgoDB.getVal());
        SqlQueryDTO queryDTO = SqlQueryDTO.builder().tableName("loader_test_orc_not_tran").build();
        IDownloader downloader = client.getDownloader(ARGODB_SOURCE_DTO, queryDTO);
        Assert.assertTrue(CollectionUtils.isNotEmpty(downloader.getMetaInfo()));
        while (!downloader.reachedEnd()) {
            System.out.println(downloader.readNext());
        }
    }

    @Test
    public void getDownloader_text() throws Exception {
        IClient client = ClientCache.getClient(DataSourceType.ArgoDB.getVal());
        SqlQueryDTO queryDTO = SqlQueryDTO.builder().tableName("loader_test_text").columns(Arrays.asList("*")).build();
        IDownloader downloader = client.getDownloader(ARGODB_SOURCE_DTO, queryDTO);
        Assert.assertTrue(CollectionUtils.isNotEmpty(downloader.getMetaInfo()));
        while (!downloader.reachedEnd()) {
            System.out.println(downloader.readNext());
        }
    }

    @Test
    public void getDownloader_par() throws Exception {
        IClient client = ClientCache.getClient(DataSourceType.ArgoDB.getVal());
        SqlQueryDTO queryDTO = SqlQueryDTO.builder().tableName("loader_test_parquet").columns(Arrays.asList("*")).build();
        IDownloader downloader = client.getDownloader(ARGODB_SOURCE_DTO, queryDTO);
        Assert.assertTrue(CollectionUtils.isNotEmpty(downloader.getMetaInfo()));
        while (!downloader.reachedEnd()) {
            System.out.println(downloader.readNext());
        }
    }

    /**
     * 支持模糊查询，和limit
     */
    @Test
    public void getTableList() {
        SqlQueryDTO queryDTO = SqlQueryDTO.builder().limit(3).tableNamePattern("test").build();
        List<String> tableList = ARGODB_CLIENT.getTableList(ARGODB_SOURCE_DTO, queryDTO);
        Assert.assertTrue(CollectionUtils.isNotEmpty(tableList));
        Assert.assertEquals(3, tableList.size());
        for (String table : tableList) {
            Assert.assertTrue(table.contains("test"));
        }
    }

    @Test
    public void getTableListBySchema() {
        SqlQueryDTO queryDTO = SqlQueryDTO.builder().schema("loader_test").build();
        List<String> tableList = ARGODB_CLIENT.getTableListBySchema(ARGODB_SOURCE_DTO, queryDTO);
        Assert.assertTrue(CollectionUtils.isEmpty(tableList));
    }

    @Test
    public void getColumnClassInfo() {
        SqlQueryDTO queryDTO = SqlQueryDTO.builder().tableName("loader_test_text").build();
        List<String> columnClassInfo = ARGODB_CLIENT.getColumnClassInfo(ARGODB_SOURCE_DTO, queryDTO);
        Assert.assertTrue(CollectionUtils.isNotEmpty(columnClassInfo));
    }

    @Test
    public void getColumnMetaData() {
        SqlQueryDTO queryDTO = SqlQueryDTO.builder().tableName("loader_test_text").build();
        List<ColumnMetaDTO> columnMetaData = ARGODB_CLIENT.getColumnMetaData(ARGODB_SOURCE_DTO, queryDTO);
        Assert.assertEquals("int", columnMetaData.get(0).getType());
        Assert.assertEquals("string", columnMetaData.get(1).getType());
        Assert.assertTrue(CollectionUtils.isNotEmpty(columnMetaData));
    }

    @Test
    public void getTableMetaComment() {
        SqlQueryDTO queryDTO = SqlQueryDTO.builder().tableName("loader_test_text").build();
        String comment = ARGODB_CLIENT.getTableMetaComment(ARGODB_SOURCE_DTO, queryDTO);
        Assert.assertEquals("table comment", comment);
    }

    @Test
    public void getDownloaderForCsv() throws Exception {
        SqlQueryDTO queryDTO = SqlQueryDTO.builder().sql("select * from loader_test_csv").build();
        IDownloader downloader = ARGODB_CLIENT.getDownloader(ARGODB_SOURCE_DTO, queryDTO);
        Assert.assertTrue(CollectionUtils.isNotEmpty(downloader.getMetaInfo()));
        while (!downloader.reachedEnd()) {
            Assert.assertNotNull(downloader.readNext());
        }
    }

    /**
     * loader_test_orc_not_tran 为空表时，判断是否能够获取元数据信息
     *
     * @throws Exception
     */
    @Test
    public void getDownloader_001() throws Exception {
        SqlQueryDTO queryDTO = SqlQueryDTO.builder().sql("select * from loader_test_orc_not_tran").build();
        IDownloader downloader = ARGODB_CLIENT.getDownloader(ARGODB_SOURCE_DTO, queryDTO);
        List<String> list = downloader.getMetaInfo();
        Assert.assertEquals(2, list.size());
        Assert.assertTrue(list.contains("id"));
        Assert.assertTrue(list.contains("name"));
    }

    @Test
    public void getPartitionColumn() {
        List<ColumnMetaDTO> data = ARGODB_CLIENT.getColumnMetaData(ARGODB_SOURCE_DTO, SqlQueryDTO.builder().tableName("loader_test_text").build());
        data.forEach(x -> System.out.println(x.getKey() + "==" + x.getPart()));
    }

    @Test
    public void getPreview2() {
        HashMap<String, String> map = new HashMap<>();
        map.put("id", "1");
        List list = ARGODB_CLIENT.getPreview(ARGODB_SOURCE_DTO, SqlQueryDTO.builder().tableName("loader_test_text").partitionColumns(map).build());
        Assert.assertTrue(CollectionUtils.isNotEmpty(list));
    }

    @Test
    public void getColumnMetaDataWithSql() {
        SqlQueryDTO sqlQueryDTO = SqlQueryDTO.builder().sql("select * from loader_test_text limit 1").build();
        List list = ARGODB_CLIENT.getColumnMetaDataWithSql(ARGODB_SOURCE_DTO, sqlQueryDTO);
        Assert.assertTrue(CollectionUtils.isNotEmpty(list));
    }

    @Test
    public void getCreateTableSql() {
        SqlQueryDTO sqlQueryDTO = SqlQueryDTO.builder().tableName("loader_test_text").build();
        String createTableSql = ARGODB_CLIENT.getCreateTableSql(ARGODB_SOURCE_DTO, sqlQueryDTO);
        Assert.assertTrue(StringUtils.isNotBlank(createTableSql));
    }

    @Test
    public void getAllDataBases() {
        SqlQueryDTO sqlQueryDTO = SqlQueryDTO.builder().build();
        List databases = ARGODB_CLIENT.getAllDatabases(ARGODB_SOURCE_DTO, sqlQueryDTO);
        Assert.assertTrue(CollectionUtils.isNotEmpty(databases));
    }

    @Test
    public void getTableText() {
        Table table = ARGODB_CLIENT.getTable(ARGODB_SOURCE_DTO, SqlQueryDTO.builder().tableName("loader_test_text").build());
        Assert.assertEquals(table.getStoreType(), "text");
    }

    @Test
    public void getTableCsv() {
        Table table = ARGODB_CLIENT.getTable(ARGODB_SOURCE_DTO, SqlQueryDTO.builder().tableName("loader_test_csv").build());
        Assert.assertEquals(table.getStoreType(), "csv");
    }

    @Test
    public void getTableOrc() {
        Table table = ARGODB_CLIENT.getTable(ARGODB_SOURCE_DTO, SqlQueryDTO.builder().tableName("loader_test_orc").build());
        Assert.assertEquals(table.getStoreType(), "orc");
    }

    @Test
    public void getTableLocation() {
        Table table = ARGODB_CLIENT.getTable(ARGODB_SOURCE_DTO, SqlQueryDTO.builder().tableName("loader_test_text").build());
        Assert.assertNotNull(table.getPath());
    }

    @Test
    public void createDb() {
        try {
            ARGODB_CLIENT.executeSqlWithoutResultSet(ARGODB_SOURCE_DTO, SqlQueryDTO.builder().sql("drop database if exists loader_test").build());
            assert ARGODB_CLIENT.createDatabase(ARGODB_SOURCE_DTO, "loader_test", "测试注释");
        } catch (Exception e) {
            // 可能失败
            System.out.println(e.getMessage());
        }
    }

    @Test
    public void isDbExists() {
        assert ARGODB_CLIENT.isDatabaseExists(ARGODB_SOURCE_DTO, "default");
    }

    @Test
    public void tableInDb() {
        assert ARGODB_CLIENT.isTableExistsInDatabase(ARGODB_SOURCE_DTO, null);
    }

    @Test
    public void tableNotInDb() {
        assert !ARGODB_CLIENT.isTableExistsInDatabase(ARGODB_SOURCE_DTO, null);
    }

    /**
     * 表为事务表
     */
    @Test
    public void tableIsTransTable() {
        Assert.assertTrue(ARGODB_CLIENT.getTable(ARGODB_SOURCE_DTO, SqlQueryDTO.builder().tableName("loader_test_orc_tran").build()).getIsTransTable());
    }

    /**
     * 表为非事务表
     */
    @Test
    public void tableIsNotTransTable() {
        Assert.assertFalse(ARGODB_CLIENT.getTable(ARGODB_SOURCE_DTO, SqlQueryDTO.builder().tableName("loader_test_orc_not_tran").build()).getIsTransTable());
    }
}
