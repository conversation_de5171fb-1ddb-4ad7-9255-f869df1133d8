/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.dtstack.dtcenter.common.loader.oss_huawei;

import com.dtstack.dtcenter.common.loader.common.nosql.AbsNoSqlClient;
import com.dtstack.dtcenter.loader.dto.SqlQueryDTO;
import com.dtstack.dtcenter.loader.dto.source.ISourceDTO;
import com.dtstack.dtcenter.loader.dto.source.OssHuaweiSourceDTO;
import com.dtstack.dtcenter.loader.dto.source.OssSourceDTO;
import com.dtstack.dtcenter.loader.exception.DtLoaderException;
import com.google.common.collect.Lists;
import com.obs.services.ObsClient;
import com.obs.services.model.ObjectListing;
import com.obs.services.model.S3Bucket;
import com.obs.services.model.S3Object;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * aws s3 Client
 *
 * <AUTHOR>
 * date：Created in 上午9:46 2021/5/6
 * company: www.dtstack.com
 */
public class Oss_huaweiClient<T> extends AbsNoSqlClient<T> {

    /**
     * s3 object 前置查询需要以 .* 结尾
     */
    private static final String SEARCH_PREFIX_SING = ".*";

    @Override
    public Boolean testCon(ISourceDTO source) {
        OssHuaweiSourceDTO sourceDTO = Oss_huaweiUtil.convertSourceDTO(source);
        ObsClient oss = null;
        try {
            oss = Oss_huaweiUtil.getClient(sourceDTO);
            oss.listBuckets();
        } catch (Exception e) {
            throw new DtLoaderException(String.format("oss_huawei connection failed : %s", e.getMessage()), e);
        } finally {
            Oss_huaweiUtil.closeAmazonS3(oss);
        }
        return true;
    }

    @Override
    public List<String> getTableList(ISourceDTO source, SqlQueryDTO queryDTO) {
        OssHuaweiSourceDTO sourceDTO = Oss_huaweiUtil.convertSourceDTO(source);
        String bucket = queryDTO.getSchema();
        if (StringUtils.isBlank(bucket)) {
            throw new DtLoaderException("bucket cannot be blank....");
        }
        String tableNamePattern = queryDTO.getTableNamePattern();
        // 是否匹配查询
        boolean isPattern = StringUtils.isNotBlank(tableNamePattern);
        // 仅支持前置匹配
        boolean isPrefix = isPattern && tableNamePattern.endsWith(SEARCH_PREFIX_SING);
        ObsClient oss = null;
        List<String> objectList=new ArrayList<>();
        try {
            oss = Oss_huaweiUtil.getClient(sourceDTO);
            ObjectListing objectListing;
            if (!isPattern) {
                 objectListing = oss.listObjects(bucket);
            } else {
                String table = isPrefix ? tableNamePattern.substring(0, tableNamePattern.length() - 2) : tableNamePattern;
                objectListing = oss.listObjects(bucket );
            }
            if (Objects.isNull(objectListing)) {
                return Lists.newArrayList();
            }
            List<S3Object> objectSummaries = objectListing.getObjectSummaries();
            if (CollectionUtils.isEmpty(objectSummaries)) {
                return Lists.newArrayList();
            }
            for (S3Object objectSummary : objectSummaries) {
                String key = objectSummary.getObjectKey();
                objectList.add(key);
            }
       } catch (Exception e) {
            throw new DtLoaderException(String.format("oss_huawei get buckets failed : %s", e.getMessage()), e);
        } finally {
            Oss_huaweiUtil.closeAmazonS3(oss);
        }
        if (isPattern && !isPrefix) {
            objectList = objectList.stream().filter(table -> StringUtils.equalsIgnoreCase(table, tableNamePattern)).collect(Collectors.toList());
        }
        if (Objects.nonNull(queryDTO.getLimit())) {
            objectList = objectList.stream().limit(queryDTO.getLimit()).collect(Collectors.toList());
        }
        return objectList;
    }

    @Override
    public List<String> getTableListBySchema(ISourceDTO source, SqlQueryDTO queryDTO) {
        return getTableList(source, queryDTO);
    }

    @Override
    public List<String> getAllDatabases(ISourceDTO source, SqlQueryDTO queryDTO) {
        OssHuaweiSourceDTO sourceDTO = Oss_huaweiUtil.convertSourceDTO(source);
        ObsClient oss = null;
        List<String> result;
        try {
            oss = Oss_huaweiUtil.getClient(sourceDTO);
            List<S3Bucket> s3Buckets = oss.listBuckets();
            result = s3Buckets.stream().map(S3Bucket::getBucketName).collect(Collectors.toList());
        } catch (Exception e) {
            throw new DtLoaderException(String.format("oss_huawei get buckets failed : %s", e.getMessage()), e);
        } finally {
            Oss_huaweiUtil.closeAmazonS3(oss);
        }
        if (StringUtils.isNotBlank(queryDTO.getSchema())) {
            result = result.stream().filter(bucket -> StringUtils.containsIgnoreCase(bucket, queryDTO.getSchema().trim())).collect(Collectors.toList());
        }
        if (Objects.nonNull(queryDTO.getLimit())) {
            result = result.stream().limit(queryDTO.getLimit()).collect(Collectors.toList());
        }
        return result;
    }
}
