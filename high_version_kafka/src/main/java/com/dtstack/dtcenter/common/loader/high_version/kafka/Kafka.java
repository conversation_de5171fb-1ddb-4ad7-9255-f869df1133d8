package com.dtstack.dtcenter.common.loader.high_version.kafka;

import com.alibaba.fastjson.JSONObject;
import com.dsg.database.datasource.dto.*;
import com.dtstack.dtcenter.common.loader.common.exception.ErrorCode;
import com.dtstack.dtcenter.common.loader.high_version.kafka.utils.KafkaUtil;
import com.dtstack.dtcenter.loader.client.IKafka;
import com.dtstack.dtcenter.loader.dto.KafkaConsumerDTO;
import com.dtstack.dtcenter.loader.dto.KafkaPartitionDTO;
import com.dtstack.dtcenter.loader.dto.KafkaTopicDTO;
import com.dtstack.dtcenter.loader.dto.SqlQueryDTO;
import com.dtstack.dtcenter.loader.dto.source.ISourceDTO;
import com.dtstack.dtcenter.loader.dto.source.KafkaSourceDTO;
import com.dtstack.dtcenter.loader.exception.DtLoaderException;

import java.util.List;
import java.util.Map;

/**
 * kafka 客户端，支持 kafka 0.10、10.11、1.x、2.x 版本
 * 支持 kafka kerberos认证(SASL/GSSAPI)、用户名密码认证(SASL/PLAIN)
 *
 * <AUTHOR>
 * date：Created in 下午4:39 2021/7/9
 * company: www.dtstack.com
 */
public class Kafka<T> implements IKafka<T> {
    @Override
    public Boolean testCon(ISourceDTO sourceDTO) {
        return null;
    }

    @Override
    public String getAllBrokersAddress(ISourceDTO sourceDTO) {
        return null;
    }

    @Override
    public List<BrokersDTO> getAllBrokersInfoList(ISourceDTO source) {
        return null;
    }

    @Override
    public List<String> getTopicList(ISourceDTO sourceDTO) {
        return null;
    }

    @Override
    public List<TopIcInfoDTO> getTopicInfoList(ISourceDTO source) {
        return null;
    }



    @Override
    public List getOffset(ISourceDTO sourceDTO, String topic) {
        return null;
    }

    @Override
    public List<List<Object>> getPreview(ISourceDTO sourceDTO, SqlQueryDTO queryDTO) {
        return getPreview(sourceDTO, queryDTO, KafkaUtil.EARLIEST);
    }

    @Override
    public List<List<Object>> getPreview(ISourceDTO sourceDTO, SqlQueryDTO queryDTO, String prevMode) {
        return null;
    }

    @Override
    public List<List<Object>> getRecordsFromKafkaByStatistics(ISourceDTO sourceDTO, Integer partition,String topic, String autoReset, String maxpoolrecords) {
        return null;
    }

    @Override
    public List<KafkaPartitionDTO> getTopicPartitions(ISourceDTO source, String topic) {
        return null;
    }

    @Override
    public List<String> consumeData(ISourceDTO source, String topic, Integer collectNum, String offsetReset, Long timestampOffset, Integer maxTimeWait) {
        return null;
    }

    @Override
    public List<String> listConsumerGroup(ISourceDTO source) {
        return null;
    }

    @Override
    public List<ConsumerInfoDTO> getConsumerInfoList(ISourceDTO source) {
        return null;
    }

    @Override
    public JSONObject listTopicInfoByGroupId(ISourceDTO sourceDTO, String groupId) {
        return null;
    }




    @Override
    public List<String> listConsumerGroupByTopic(ISourceDTO source, String topic) {
        return null;
    }

    @Override
    public List<KafkaConsumerDTO> getGroupInfoByGroupId(ISourceDTO source, String groupId) {
        return null;
    }

    @Override
    public List<KafkaConsumerDTO> getGroupInfoByTopic(ISourceDTO source, String topic) {
        return null;
    }

    @Override
    public List<KafkaConsumerDTO> getGroupInfoByGroupIdAndTopic(ISourceDTO source, String groupId, String topic) {
        return null;
    }

    @Override
    public Integer getBrokerLeaderSkewed(ISourceDTO source, String topic) {
        return null;
    }

    @Override
    public Integer getBrokerSkewed(ISourceDTO source, String topic) {
        return null;
    }

    @Override
    public Integer getBrokerSpread(ISourceDTO source, String topic) {
        return null;
    }

    @Override
    public String getBrokerKafkaVersion(String host, Integer port, String id) {
        return null;
    }

    @Override
    public List<TopIcMetaInfoDTO> getTopIcMetaByTopIc(ISourceDTO source, String topIcName) {
        return null;
    }

    @Override
    public String getBrokerCpuUse(ISourceDTO source, String host, Integer port){
        return null;
    }

    @Override
    public String getBrokerMemoryUse(ISourceDTO source, String host, Integer port)  {
        return null;
    }

    @Override
    public String getBrokerMemoryUsePercent(ISourceDTO source, String host, Integer port) {
        return null;
    }

    @Override
    public Map<String, KafkaMonitorDTO> getTopicMonitor(ISourceDTO source, String topic) {
        return null;
    }

    @Override
    public Map<String, KafkaMonitorDTO> getOnlineAllBrokersMBean(String host, Integer port) {
        return null;
    }

    @Override
    public double getCpuUsed(ISourceDTO source) {
        return 0;
    }

    @Override
    public double getOSMemory(ISourceDTO source) {
        return 0;
    }

    @Override
    public Boolean createTopicFromBroker(ISourceDTO sourceDTO, KafkaTopicDTO kafkaTopicDTO) {
       return true;
    }

    @Override
    public Boolean createTopicPartitions(ISourceDTO sourceDTO, String topicName, Integer partitions) {
        KafkaSourceDTO kafkaSourceDTO = (KafkaSourceDTO) sourceDTO;
        return  KafkaUtil.createTopicPartitions(kafkaSourceDTO, topicName, partitions);
    }

    @Override
    public Boolean deleteTOpic(ISourceDTO sourceDTO, String topicName) {
        return true;
    }

    @Override
    public List<KpiInfoDTO>  getBrokerKpiInfos(ISourceDTO sourceDTO) {
        return null;
    }

    @Override
    public long getLogSizeByTopic(ISourceDTO sourceDTO, String topic) {
        return 0;
    }

    @Override
    public long getCapacityByTopic(ISourceDTO sourceDTO, String topic) {
        return 0;
    }

    @Override
    public long getByteInByTopic(ISourceDTO sourceDTO, String topic) {
        return 0;
    }

    @Override
    public long getByteOutByTopic(ISourceDTO sourceDTO, String topic) {
        return 0;
    }

    @Override
    public List<OffsetInfoDTO> getTopicOffset(ISourceDTO sourceDTO, String topicName, String groupName) {
        return null;
    }

    @Override
    public long getKafkaProducerLogSizeByTopic(ISourceDTO sourceDTO, String topic) {
        return 0;
    }

    @Override
    public String getConsumerStatus(ISourceDTO sourceDTO, String groupId) {
        return null;
    }

    @Override
    public OffsetInfoDTO getTopicMessageStatistics(ISourceDTO sourceDTO, String topic, String groupName) {
        return null;
    }

    @Override
    public JSONObject getAllActiveTopicsTree(ISourceDTO sourceDTO) {
        return null;
    }


    @Override
    public List<T> getAllPartitions(ISourceDTO source, String topic) {
        throw new DtLoaderException(ErrorCode.NOT_SUPPORT.getDesc());
    }
}
