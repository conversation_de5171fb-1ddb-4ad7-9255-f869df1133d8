/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.dtstack.dtcenter.common.loader.hdfs.client;

import com.alibaba.fastjson.JSON;
import com.dsg.database.datasource.utils.DsgDistCPParams;
import com.dtstack.dtcenter.common.loader.common.utils.ReflectUtil;
import com.dtstack.dtcenter.common.loader.hadoop.hdfs.HadoopConfUtil;
import com.dtstack.dtcenter.common.loader.hadoop.hdfs.HdfsOperator;
import com.dtstack.dtcenter.common.loader.hadoop.util.KerberosLoginUtil;
import com.dtstack.dtcenter.common.loader.hdfs.YarnConfUtil;
import com.dtstack.dtcenter.common.loader.hdfs.downloader.HdfsFileDownload;
import com.dtstack.dtcenter.common.loader.hdfs.downloader.YarnLogDownload.YarnTFileDownload;
import com.dtstack.dtcenter.common.loader.hdfs.downloader.tableDownload.HiveORCDownload;
import com.dtstack.dtcenter.common.loader.hdfs.downloader.tableDownload.HiveParquetDownload;
import com.dtstack.dtcenter.common.loader.hdfs.downloader.tableDownload.HiveTextDownload;
import com.dtstack.dtcenter.common.loader.hdfs.dsgdistcp.HdfsDistCP;
import com.dtstack.dtcenter.common.loader.hdfs.enums.DsgMrJobState;
import com.dtstack.dtcenter.common.loader.hdfs.fileMerge.core.CombineMergeBuilder;
import com.dtstack.dtcenter.common.loader.hdfs.fileMerge.core.CombineServer;
import com.dtstack.dtcenter.common.loader.hdfs.hdfswriter.HdfsOrcWriter;
import com.dtstack.dtcenter.common.loader.hdfs.hdfswriter.HdfsParquetWriter;
import com.dtstack.dtcenter.common.loader.hdfs.hdfswriter.HdfsTextWriter;
import com.dtstack.dtcenter.common.loader.hdfs.util.StringUtil;
import com.dtstack.dtcenter.loader.IDownloader;
import com.dtstack.dtcenter.loader.client.IHdfsFile;
import com.dtstack.dtcenter.loader.dto.FileStatus;
import com.dtstack.dtcenter.loader.dto.*;
import com.dtstack.dtcenter.loader.dto.source.HdfsSourceDTO;
import com.dtstack.dtcenter.loader.dto.source.ISourceDTO;
import com.dtstack.dtcenter.loader.enums.FileFormat;
import com.dtstack.dtcenter.loader.exception.DtLoaderException;
import com.dtstack.dtcenter.loader.kerberos.HadoopConfTool;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.hadoop.CustomOutputCommitter;
import org.apache.hadoop.conf.Configuration;
import org.apache.hadoop.fs.*;
import org.apache.hadoop.hdfs.HAUtil;
import org.apache.hadoop.hive.ql.io.orc.OrcFile;
import org.apache.hadoop.mapred.JobClient;
import org.apache.hadoop.mapred.JobConf;
import org.apache.hadoop.mapred.JobContextImpl;
import org.apache.hadoop.mapred.RunningJob;
import org.apache.hadoop.tools.DistCpOptions;
import org.apache.hadoop.tools.OptionsParser;
import org.apache.hadoop.yarn.logaggregation.filecontroller.ifile.LogAggregationIndexedFileController;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.net.InetSocketAddress;
import java.security.PrivilegedAction;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @company: www.dtstack.com
 * <AUTHOR>
 * @Date ：Created in 14:50 2020/8/10
 * @Description：HDFS 文件操作实现类
 */
@Slf4j
public class HdfsFileClient implements IHdfsFile {

    private static final String PATH_DELIMITER = "/";

    // yarn聚合日志格式，默认TFIle
    private static final String LOG_FORMAT = "yarn.log-aggregation.file-formats";

    // null 名称的字段名
    private static final String NULL_COLUMN = "null";

    @Override
    public FileStatus getStatus(ISourceDTO iSource, String location) {
        org.apache.hadoop.fs.FileStatus hadoopFileStatus = getHadoopStatus(iSource, location);

        return FileStatus.builder()
                .length(hadoopFileStatus.getLen())
                .access_time(hadoopFileStatus.getAccessTime())
                .block_replication(hadoopFileStatus.getReplication())
                .blocksize(hadoopFileStatus.getBlockSize())
                .group(hadoopFileStatus.getGroup())
                .isdir(hadoopFileStatus.isDirectory())
                .modification_time(hadoopFileStatus.getModificationTime())
                .owner(hadoopFileStatus.getOwner())
                .path(hadoopFileStatus.getPath().toString())
                .build();
    }

    @Override
    public IDownloader getLogDownloader(ISourceDTO iSource, SqlQueryDTO queryDTO) {
        HdfsSourceDTO hdfsSourceDTO = (HdfsSourceDTO) iSource;
        return KerberosLoginUtil.loginWithUGI(hdfsSourceDTO.getKerberosConfig()).doAs(
                (PrivilegedAction<IDownloader>) () -> {
                    try {
                        return createYarnLogDownload(hdfsSourceDTO);
                    } catch (Exception e) {
                        throw new DtLoaderException(String.format("create downloader exception,%s", e.getMessage()), e);
                    }
                }
        );
    }

    /**
     * 创建yarn 聚合日志下载器，区分ifile、tfile格式
     *
     * @param hdfsSourceDTO 数据源信息
     * @return yarn日志下载器
     * @throws Exception 异常信息
     */
    private IDownloader createYarnLogDownload(HdfsSourceDTO hdfsSourceDTO) throws Exception {
        IDownloader yarnDownload;
        Configuration configuration = YarnConfUtil.getFullConfiguration(null, hdfsSourceDTO.getConfig(), hdfsSourceDTO.getYarnConf(), hdfsSourceDTO.getKerberosConfig());
        String fileFormat = configuration.get(LOG_FORMAT);
        boolean containerFiledExists = Arrays.stream(HdfsSourceDTO.class.getDeclaredFields())
                .anyMatch(field -> "ContainerId".equalsIgnoreCase(field.getName()));
        if (StringUtils.isNotBlank(fileFormat) && StringUtils.containsIgnoreCase(fileFormat, "IFile")) {
            if (!containerFiledExists || StringUtils.isEmpty(hdfsSourceDTO.getContainerId())) {
                yarnDownload = new LogAggregationIndexedFileController(hdfsSourceDTO.getDefaultFS(), hdfsSourceDTO.getUser(), hdfsSourceDTO.getConfig(), hdfsSourceDTO.getYarnConf(), hdfsSourceDTO.getAppIdStr(), hdfsSourceDTO.getReadLimit(), hdfsSourceDTO.getLogType(), hdfsSourceDTO.getKerberosConfig(), null);
            } else {
                yarnDownload = new LogAggregationIndexedFileController(hdfsSourceDTO.getDefaultFS(), hdfsSourceDTO.getUser(), hdfsSourceDTO.getConfig(), hdfsSourceDTO.getYarnConf(), hdfsSourceDTO.getAppIdStr(), hdfsSourceDTO.getReadLimit(), hdfsSourceDTO.getLogType(), hdfsSourceDTO.getKerberosConfig(), hdfsSourceDTO.getContainerId());
            }
        } else {
            if (!containerFiledExists || StringUtils.isEmpty(hdfsSourceDTO.getContainerId())) {
                yarnDownload = new YarnTFileDownload(hdfsSourceDTO.getDefaultFS(), hdfsSourceDTO.getUser(), hdfsSourceDTO.getConfig(), hdfsSourceDTO.getYarnConf(), hdfsSourceDTO.getAppIdStr(), hdfsSourceDTO.getReadLimit(), hdfsSourceDTO.getLogType(), hdfsSourceDTO.getKerberosConfig());
            } else {
                yarnDownload = new YarnTFileDownload(hdfsSourceDTO.getDefaultFS(), hdfsSourceDTO.getUser(), hdfsSourceDTO.getConfig(), hdfsSourceDTO.getYarnConf(), hdfsSourceDTO.getAppIdStr(), hdfsSourceDTO.getReadLimit(), hdfsSourceDTO.getLogType(), hdfsSourceDTO.getKerberosConfig(), hdfsSourceDTO.getContainerId());
            }
        }
        yarnDownload.configure();
        return yarnDownload;
    }

    @Override
    public IDownloader getFileDownloader(ISourceDTO iSource, String path) {
        HdfsSourceDTO hdfsSourceDTO = (HdfsSourceDTO) iSource;
        return KerberosLoginUtil.loginWithUGI(hdfsSourceDTO.getKerberosConfig()).doAs(
                (PrivilegedAction<IDownloader>) () -> {
                    try {
                        HdfsFileDownload hdfsFileDownload = new HdfsFileDownload(hdfsSourceDTO.getDefaultFS(), hdfsSourceDTO.getConfig(), path, hdfsSourceDTO.getYarnConf(), hdfsSourceDTO.getKerberosConfig());
                        hdfsFileDownload.configure();
                        return hdfsFileDownload;
                    } catch (Exception e) {
                        throw new DtLoaderException(String.format("Create file downloader exception,%s", e.getMessage()), e);
                    }
                }
        );
    }

    /**
     * 获取 HADOOP 文件信息
     *
     * @param source
     * @param location
     * @return
     * @throws Exception
     */
    private org.apache.hadoop.fs.FileStatus getHadoopStatus(ISourceDTO source, String location) {
        HdfsSourceDTO hdfsSourceDTO = (HdfsSourceDTO) source;

        FileSystem fs = HdfsOperator.getFileSystem(hdfsSourceDTO.getKerberosConfig(), hdfsSourceDTO.getConfig(), hdfsSourceDTO.getDefaultFS());
        return HdfsOperator.getFileStatus(fs, location);
    }

    @Override
    public boolean downloadFileFromHdfs(ISourceDTO source, String remotePath, String localDir) {
        HdfsSourceDTO hdfsSourceDTO = (HdfsSourceDTO) source;
        FileSystem fs = HdfsOperator.getFileSystem(hdfsSourceDTO.getKerberosConfig(), hdfsSourceDTO.getConfig(), hdfsSourceDTO.getDefaultFS());
        HdfsOperator.copyToLocal(fs, remotePath, localDir);
        return true;
    }

    @Override
    public boolean uploadLocalFileToHdfs(ISourceDTO source, String localFilePath, String remotePath) {
        HdfsSourceDTO hdfsSourceDTO = (HdfsSourceDTO) source;
        FileSystem fs = HdfsOperator.getFileSystem(hdfsSourceDTO.getKerberosConfig(), hdfsSourceDTO.getConfig(), hdfsSourceDTO.getDefaultFS());
        HdfsOperator.uploadLocalFileToHdfs(fs, localFilePath, remotePath);
        return true;
    }

    @Override
    public boolean uploadInputStreamToHdfs(ISourceDTO source, byte[] bytes, String remotePath) {
        HdfsSourceDTO hdfsSourceDTO = (HdfsSourceDTO) source;
        FileSystem fs = HdfsOperator.getFileSystem(hdfsSourceDTO.getKerberosConfig(), hdfsSourceDTO.getConfig(), hdfsSourceDTO.getDefaultFS());
        return HdfsOperator.uploadInputStreamToHdfs(fs, bytes, remotePath);
    }

    @Override
    public boolean createDir(ISourceDTO source, String remotePath, Short permission) {
        HdfsSourceDTO hdfsSourceDTO = (HdfsSourceDTO) source;
        FileSystem fs = HdfsOperator.getFileSystem(hdfsSourceDTO.getKerberosConfig(), hdfsSourceDTO.getConfig(), hdfsSourceDTO.getDefaultFS());
        return HdfsOperator.createDir(fs, remotePath, permission);
    }

    @Override
    public boolean isFileExist(ISourceDTO source, String remotePath) {
        HdfsSourceDTO hdfsSourceDTO = (HdfsSourceDTO) source;
        FileSystem fs = HdfsOperator.getFileSystem(hdfsSourceDTO.getKerberosConfig(), hdfsSourceDTO.getConfig(), hdfsSourceDTO.getDefaultFS());
        return HdfsOperator.isFileExist(fs, remotePath);
    }

    @Override
    public boolean checkAndDelete(ISourceDTO source, String remotePath) {
        HdfsSourceDTO hdfsSourceDTO = (HdfsSourceDTO) source;
        FileSystem fs = HdfsOperator.getFileSystem(hdfsSourceDTO.getKerberosConfig(), hdfsSourceDTO.getConfig(), hdfsSourceDTO.getDefaultFS());
        return HdfsOperator.checkAndDelete(fs, remotePath);
    }

    @Override
    public boolean delete(ISourceDTO source, String remotePath, boolean recursive) {
        HdfsSourceDTO hdfsSourceDTO = (HdfsSourceDTO) source;
        return KerberosLoginUtil.loginWithUGI(hdfsSourceDTO.getKerberosConfig()).doAs(
                (PrivilegedAction<Boolean>) () -> {
                    try {
                        Configuration conf = HadoopConfUtil.getHdfsConf(hdfsSourceDTO.getDefaultFS(), hdfsSourceDTO.getConfig(), hdfsSourceDTO.getKerberosConfig());
                        FileSystem fs = FileSystem.get(conf);
                        log.info("delete hdfs file ,remotePath :{}", remotePath);
                        return fs.delete(new Path(remotePath), recursive);
                    } catch (Exception e) {
                        throw new DtLoaderException(String.format("Target path deletion exception,%s", e.getMessage()), e);
                    }
                }
        );
    }

    @Override
    public boolean copyDirector(ISourceDTO source, String src, String dist) {
        HdfsSourceDTO hdfsSourceDTO = (HdfsSourceDTO) source;
        return KerberosLoginUtil.loginWithUGI(hdfsSourceDTO.getKerberosConfig()).doAs(
                (PrivilegedAction<Boolean>) () -> {
                    try {
                        Path srcPath = new Path(src);
                        Path distPath = new Path(dist);
                        Configuration conf = HadoopConfUtil.getHdfsConf(hdfsSourceDTO.getDefaultFS(), hdfsSourceDTO.getConfig(), hdfsSourceDTO.getKerberosConfig());
                        FileSystem fs = FileSystem.get(conf);
                        if (fs.exists(srcPath)) {
                            //判断是不是文件夹
                            if (fs.isDirectory(srcPath)) {
                                if (!FileUtil.copy(fs, srcPath, fs, distPath, false, conf)) {
                                    throw new DtLoaderException("copy " + src + " to " + dist + " failed");
                                }
                            } else {
                                throw new DtLoaderException(src + "is not a directory");
                            }
                        } else {
                            throw new DtLoaderException(src + " is not exists");
                        }
                        return true;
                    } catch (Exception e) {
                        throw new DtLoaderException(String.format("Target path deletion exception,%s", e.getMessage()), e);
                    }
                }
        );
    }

    @Override
    public boolean fileMerge(ISourceDTO source, String src, String mergePath, FileFormat fileFormat, Long maxCombinedFileSize, Long needCombineFileSizeLimit) {
        HdfsSourceDTO hdfsSourceDTO = (HdfsSourceDTO) source;
        return KerberosLoginUtil.loginWithUGI(hdfsSourceDTO.getKerberosConfig()).doAs(
                (PrivilegedAction<Boolean>) () -> {
                    try {
                        Configuration conf = HadoopConfUtil.getHdfsConf(hdfsSourceDTO.getDefaultFS(), hdfsSourceDTO.getConfig(), hdfsSourceDTO.getKerberosConfig());
                        CombineServer build = new CombineMergeBuilder()
                                .sourcePath(src)
                                .mergedPath(mergePath)
                                .fileType(fileFormat)
                                .maxCombinedFileSize(maxCombinedFileSize)
                                .needCombineFileSizeLimit(needCombineFileSizeLimit)
                                .configuration(conf)
                                .build();
                        build.combine();
                        return true;
                    } catch (Exception e) {
                        throw new DtLoaderException(String.format("File merge exception：%s", e.getMessage()), e);
                    }
                }
        );
    }

    @Override
    public long getDirSize(ISourceDTO source, String remotePath) {
        HdfsSourceDTO hdfsSourceDTO = (HdfsSourceDTO) source;
        FileSystem fs = HdfsOperator.getFileSystem(hdfsSourceDTO.getKerberosConfig(), hdfsSourceDTO.getConfig(), hdfsSourceDTO.getDefaultFS());
        return HdfsOperator.getDirSize(fs, remotePath);
    }

    @Override
    public boolean deleteFiles(ISourceDTO source, List<String> fileNames) {
        HdfsSourceDTO hdfsSourceDTO = (HdfsSourceDTO) source;
        FileSystem fs = HdfsOperator.getFileSystem(hdfsSourceDTO.getKerberosConfig(), hdfsSourceDTO.getConfig(), hdfsSourceDTO.getDefaultFS());
        return HdfsOperator.deleteFiles(fs, fileNames);
    }

    @Override
    public boolean isDirExist(ISourceDTO source, String remotePath) {
        HdfsSourceDTO hdfsSourceDTO = (HdfsSourceDTO) source;
        FileSystem fs = HdfsOperator.getFileSystem(hdfsSourceDTO.getKerberosConfig(), hdfsSourceDTO.getConfig(), hdfsSourceDTO.getDefaultFS());
        return HdfsOperator.isDirExist(fs, remotePath);
    }

    @Override
    public boolean setPermission(ISourceDTO source, String remotePath, String mode) {
        HdfsSourceDTO hdfsSourceDTO = (HdfsSourceDTO) source;
        FileSystem fs = HdfsOperator.getFileSystem(hdfsSourceDTO.getKerberosConfig(), hdfsSourceDTO.getConfig(), hdfsSourceDTO.getDefaultFS());
        return HdfsOperator.setPermission(fs, remotePath, mode);
    }

    @Override
    public boolean rename(ISourceDTO source, String src, String dist) {
        HdfsSourceDTO hdfsSourceDTO = (HdfsSourceDTO) source;
        FileSystem fs = HdfsOperator.getFileSystem(hdfsSourceDTO.getKerberosConfig(), hdfsSourceDTO.getConfig(), hdfsSourceDTO.getDefaultFS());
        return HdfsOperator.rename(fs, src, dist);
    }

    @Override
    public boolean copyFile(ISourceDTO source, String src, String dist, boolean isOverwrite) {
        HdfsSourceDTO hdfsSourceDTO = (HdfsSourceDTO) source;
        FileSystem fs = HdfsOperator.getFileSystem(hdfsSourceDTO.getKerberosConfig(), hdfsSourceDTO.getConfig(), hdfsSourceDTO.getDefaultFS());
        try {
            return HdfsOperator.copyFile(fs, src, dist, isOverwrite);
        } catch (IOException e) {
            throw new DtLoaderException(String.format("Copying files in hdfs is abnormal : %s", e.getMessage()), e);
        }
    }

    @Override
    public List<FileStatus> listStatus(ISourceDTO source, String remotePath) {
        HdfsSourceDTO hdfsSourceDTO = (HdfsSourceDTO) source;
        FileSystem fs = HdfsOperator.getFileSystem(hdfsSourceDTO.getKerberosConfig(), hdfsSourceDTO.getConfig(), hdfsSourceDTO.getDefaultFS());
        try {
            return transferFileStatus(HdfsOperator.listStatus(fs, remotePath));
        } catch (IOException e) {
            throw new DtLoaderException(String.format("The status of the file or folder under the target path is abnormal : %s", e.getMessage()), e);
        }
    }

    @Override
    public List<String> listAllFilePath(ISourceDTO source, String remotePath) {
        HdfsSourceDTO hdfsSourceDTO = (HdfsSourceDTO) source;
        FileSystem fs = HdfsOperator.getFileSystem(hdfsSourceDTO.getKerberosConfig(), hdfsSourceDTO.getConfig(), hdfsSourceDTO.getDefaultFS());
        try {
            return HdfsOperator.listAllFilePath(fs, remotePath);
        } catch (IOException e) {
            throw new DtLoaderException(String.format("Obtaining all files in the target path is abnormal : %s", e.getMessage()), e);
        }
    }

    @Override
    public List<FileStatus> listAllFiles(ISourceDTO source, String remotePath, boolean isIterate) {
        HdfsSourceDTO hdfsSourceDTO = (HdfsSourceDTO) source;
        FileSystem fs = HdfsOperator.getFileSystem(hdfsSourceDTO.getKerberosConfig(), hdfsSourceDTO.getConfig(), hdfsSourceDTO.getDefaultFS());
        return listFiles(fs, remotePath, isIterate);
    }

    @Override
    public boolean copyToLocal(ISourceDTO source, String srcPath, String dstPath) {
        HdfsSourceDTO hdfsSourceDTO = (HdfsSourceDTO) source;
        FileSystem fs = HdfsOperator.getFileSystem(hdfsSourceDTO.getKerberosConfig(), hdfsSourceDTO.getConfig(), hdfsSourceDTO.getDefaultFS());
        return HdfsOperator.copyToLocal(fs, srcPath, dstPath);
    }

    @Override
    public boolean copyFromLocal(ISourceDTO source, String srcPath, String dstPath, boolean overwrite) {
        HdfsSourceDTO hdfsSourceDTO = (HdfsSourceDTO) source;
        FileSystem fs = HdfsOperator.getFileSystem(hdfsSourceDTO.getKerberosConfig(), hdfsSourceDTO.getConfig(), hdfsSourceDTO.getDefaultFS());
        return HdfsOperator.copyFromLocal(fs, srcPath, dstPath, overwrite);
    }

    @Override
    public IDownloader getDownloaderByFormat(ISourceDTO source, String tableLocation, List<String> columnNames, String fieldDelimiter, String fileFormat) {
        return null;
    }

    @Override
    public IDownloader getDownloaderByFormatWithType(ISourceDTO source, String tableLocation, List<ColumnMetaDTO> allColumns, List<String> filterColumns, Map<String, String> filterPartition, List<String> partitions, String fieldDelimiter, String fileFormat) {
        HdfsSourceDTO hdfsSourceDTO = (HdfsSourceDTO) source;
        Configuration conf = HadoopConfUtil.getHdfsConf(hdfsSourceDTO.getDefaultFS(), hdfsSourceDTO.getConfig(), hdfsSourceDTO.getKerberosConfig());
        // 普通字段集合
        ArrayList<ColumnMetaDTO> commonColumn = new ArrayList<>();
        // 分区字段集合
        ArrayList<String> partitionColumns = new ArrayList<>();
        if (CollectionUtils.isEmpty(allColumns)) {
            throw new DtLoaderException("allColumns 字段信息不能为空");
        }
        for (ColumnMetaDTO columnMetaDatum : allColumns) {
            // 非分区字段
            if (columnMetaDatum.getPart()) {
                partitionColumns.add(columnMetaDatum.getKey());
                continue;
            }
            commonColumn.add(columnMetaDatum);
        }
        // 需要的字段索引（包括分区字段索引）
        List<Integer> needIndex = Lists.newArrayList();
        // columns字段不为空且不包含 * 时获取指定字段的数据
        if (CollectionUtils.isNotEmpty(filterColumns) && !filterColumns.contains("*")) {
            // 保证查询字段的顺序!
            for (String column : filterColumns) {
                if (NULL_COLUMN.equalsIgnoreCase(column)) {
                    needIndex.add(Integer.MAX_VALUE);
                    continue;
                }
                // 判断查询字段是否存在
                boolean check = false;
                for (int j = 0; j < allColumns.size(); j++) {
                    if (column.equalsIgnoreCase(allColumns.get(j).getKey())) {
                        needIndex.add(j);
                        check = true;
                        break;
                    }
                }
                if (!check) {
                    throw new DtLoaderException("The query field does not exist! Field name：" + column);
                }
            }
        }
        return KerberosLoginUtil.loginWithUGI(hdfsSourceDTO.getKerberosConfig()).doAs(
                (PrivilegedAction<IDownloader>) () -> {
                    try {
                        return createDownloader(fileFormat, conf, tableLocation, commonColumn, fieldDelimiter, partitionColumns, needIndex, filterPartition, partitions, hdfsSourceDTO.getKerberosConfig());
                    } catch (Exception e) {
                        throw new DtLoaderException(String.format("create downloader exception : %s", e.getMessage()), e);
                    }
                }
        );
    }

    /**
     * 根据存储格式创建对应的hiveDownloader
     *
     * @param storageMode      存储格式
     * @param conf             配置
     * @param tableLocation    表hdfs路径
     * @param columns          字段集合
     * @param fieldDelimiter   textFile 表列分隔符
     * @param partitionColumns 分区字段集合
     * @param needIndex        需要查询的字段索引位置
     * @param filterPartitions 需要查询的分区
     * @param partitions       全部分区
     * @param kerberosConfig   kerberos 配置
     * @return downloader 数据下载器
     * @throws Exception 异常信息
     */
    private IDownloader createDownloader(String storageMode, Configuration conf, String tableLocation,
                                         List<ColumnMetaDTO> columns, String fieldDelimiter,
                                         ArrayList<String> partitionColumns, List<Integer> needIndex,
                                         Map<String, String> filterPartitions, List<String> partitions,
                                         Map<String, Object> kerberosConfig) throws Exception {
        List<String> columnNames = columns.stream().map(ColumnMetaDTO::getKey).collect(Collectors.toList());
        if (StringUtils.equalsIgnoreCase(FileFormat.TEXT.getVal(), storageMode)) {
            HiveTextDownload hiveTextDownload = new HiveTextDownload(conf, tableLocation, columnNames,
                    fieldDelimiter, partitionColumns, filterPartitions,
                    needIndex, partitions, kerberosConfig);
            hiveTextDownload.configure();
            return hiveTextDownload;
        }

        if (StringUtils.equalsIgnoreCase(FileFormat.ORC.getVal(), storageMode)) {
            HiveORCDownload hiveORCDownload = new HiveORCDownload(conf, tableLocation, columnNames,
                    partitionColumns, needIndex, partitions, kerberosConfig);
            hiveORCDownload.configure();
            return hiveORCDownload;
        }

        if (StringUtils.equalsIgnoreCase(FileFormat.PARQUET.getVal(), storageMode)) {
            HiveParquetDownload hiveParquetDownload = new HiveParquetDownload(conf, tableLocation, columns,
                    partitionColumns, needIndex, filterPartitions, partitions, kerberosConfig);
            hiveParquetDownload.configure();
            return hiveParquetDownload;
        }
        throw new DtLoaderException("This storage type file is not currently supported for writing to hdfs");
    }

    @Override
    public List<ColumnMetaDTO> getColumnList(ISourceDTO source, SqlQueryDTO queryDTO, String fileFormat) {
        HdfsSourceDTO hdfsSourceDTO = (HdfsSourceDTO) source;
        try {
            return getColumnListOnFileFormat(hdfsSourceDTO, queryDTO, fileFormat);
        } catch (IOException e) {
            throw new DtLoaderException(String.format("Failed to get column information : %s", e.getMessage()), e);
        }
    }

    @Override
    public int writeByPos(ISourceDTO source, HdfsWriterDTO hdfsWriterDTO) {
        HdfsSourceDTO hdfsSourceDTO = (HdfsSourceDTO) source;
        return KerberosLoginUtil.loginWithUGI(hdfsSourceDTO.getKerberosConfig()).doAs(
                (PrivilegedAction<Integer>) () -> {
                    try {
                        return writeByPosWithFileFormat(hdfsSourceDTO, hdfsWriterDTO);
                    } catch (Exception e) {
                        throw new DtLoaderException(String.format("Obtaining the field information of the hdfs file is abnormal : %s", e.getMessage()), e);
                    }
                }
        );
    }

    @Override
    public int writeByName(ISourceDTO source, HdfsWriterDTO hdfsWriterDTO) {
        HdfsSourceDTO hdfsSourceDTO = (HdfsSourceDTO) source;
        return KerberosLoginUtil.loginWithUGI(hdfsSourceDTO.getKerberosConfig()).doAs(
                (PrivilegedAction<Integer>) () -> {
                    try {
                        return writeByNameWithFileFormat(hdfsSourceDTO, hdfsWriterDTO);
                    } catch (Exception e) {
                        throw new DtLoaderException(String.format("Obtaining the field information of the hdfs file is abnormal : %s", e.getMessage()), e);
                    }
                }
        );
    }

    @Override
    public HDFSContentSummary getContentSummary(ISourceDTO source, String hdfsDirPath) {
        return getContentSummary(source, Lists.newArrayList(hdfsDirPath)).get(0);
    }

    @Override
    public String startDistcp(ISourceDTO source, DsgDistCPParams dsgDistCPParams, String resourceJarPath, String callUrl, String logOutPutPath) {
        HdfsSourceDTO hdfsSourceDTO = (HdfsSourceDTO) source;
        dsgDistCPParams.setKerberosConfig(hdfsSourceDTO.getKerberosConfig());
        String config = hdfsSourceDTO.getConfig();
        Map<String, String> confMap = (Map<String, String>) JSON.parse(config);
        Configuration conf = new Configuration();
        confMap.keySet().forEach(item -> {
            if (null == confMap.get(item) || StringUtils.isEmpty(confMap.get(item))) {
                conf.set(item, "");
            } else {
                conf.set(item, confMap.get(item));
            }
        });
        conf.set("mapreduce.app-submission.cross-platform", "true");
        conf.set("mapred.jar", resourceJarPath);
        if (null != dsgDistCPParams.getKerberosConfig()) {
            if (dsgDistCPParams.getKerberosConfig().size() != 0) {
                try {
                    //Kerberos 认证属性
                    String principal = MapUtils.getString(dsgDistCPParams.getKerberosConfig(), HadoopConfTool.PRINCIPAL);
                    String keytab = MapUtils.getString(dsgDistCPParams.getKerberosConfig(), HadoopConfTool.PRINCIPAL_FILE);
                    String krb5Conf = MapUtils.getString(dsgDistCPParams.getKerberosConfig(), HadoopConfTool.KEY_JAVA_SECURITY_KRB5_CONF);
                    conf.set("ipc.client.fallback-to-simple-auth-allowed", "true");
                    conf.set(HadoopConfTool.HADOOP_SECURITY_AUTHORIZATION, "kerberos");
                    conf.set(HadoopConfTool.HADOOP_SECURITY_KERBEROS_PRINCIPAL, principal);
                    conf.set(HadoopConfTool.HADOOP_SECURITY_KERBEROS_KEYTAB, keytab);
                    conf.set("logOutPutPath", logOutPutPath);
                    // 加载 krb5.conf 文件
                    File krb5File = new File(krb5Conf);
                    Properties krb5Properties = new Properties();
                    krb5Properties.load(new FileInputStream(krb5File));
                    String defaultRealm = krb5Properties.getProperty("default_realm");
                    System.setProperty("java.security.krb5.realm", defaultRealm);
                    // 获取 KDC 主机名
                    String kdcHostname = krb5Properties.getProperty("kdc");
                    System.setProperty("java.security.krb5.kdc", kdcHostname);
                    System.setProperty("java.security.krb5.conf", krb5Conf);
                    Map<String, Object> kerberosConfig = dsgDistCPParams.getKerberosConfig();
                    kerberosConfig.put(HadoopConfTool.HADOOPCONFIG, conf);
                    dsgDistCPParams.setKerberosConfig(kerberosConfig);
                } catch (Exception e) {
                    e.printStackTrace();
                    throw new RuntimeException("解析kerberos配置文件失败" + e.getMessage());
                }
            }


        }
        DistCpOptions options = OptionsParser.parse(new String[]{
                dsgDistCPParams.getCpModel(),
                dsgDistCPParams.getActiveNNBySource() + dsgDistCPParams.getSourcePath(),
                dsgDistCPParams.getActiveNNByTarget() + dsgDistCPParams.getTargetPath()});
        options.setAppend(true);
        options.setCopyStrategy("dynamic");//动态分配策略
        options.setTargetPathExists(true);
        String owner = "";
        if (null == dsgDistCPParams.getKerberosConfig() || dsgDistCPParams.getKerberosConfig().isEmpty()) {
            owner = "hdfs";
        }
        try {
           /* if(MapUtils.isEmpty(dsgDistCPParams.getKerberosConfig())){
                FileSystem fileSystem = FileSystem.get(conf);
                org.apache.hadoop.fs.FileStatus fileStatus = fileSystem.getFileStatus(new Path(dsgDistCPParams.getSourcePath()));
                owner = fileStatus.getOwner();
                System.out.println(owner);
            }*/
        } catch (Exception e) {
            e.printStackTrace();
            throw new RuntimeException(e.getMessage());
        }

        return KerberosLoginUtil.loginWithUGI(dsgDistCPParams.getKerberosConfig(), owner).doAs(
                (PrivilegedAction<String>) () -> {
                    String jobID = "";
                    try {
                        HdfsDistCP distcp = new HdfsDistCP(conf, options, callUrl, logOutPutPath);
//        distcp.setYarnQueue(params.getYarnQueue());

                        org.apache.hadoop.mapreduce.Job job = distcp.execute();
                        log.info("DistCp Completed Successfully");
                        jobID = job.getJobID().toString();
                        log.info(jobID);
                    } catch (Exception e) {
                        log.error(e.getMessage());
                        e.printStackTrace();
                        throw new RuntimeException(e.getMessage());
                    }

                    return jobID;
                }
        );

    }

    @Override
    public Boolean stopMrJob(ISourceDTO source, String jobId, String ip, Integer port) {
        HdfsSourceDTO hdfsSourceDTO = (HdfsSourceDTO) source;
        String config = hdfsSourceDTO.getConfig();
        Map<String, String> confMap = (Map<String, String>) JSON.parse(config);
        Configuration conf = new Configuration();
        confMap.keySet().forEach(item -> {
            if (null == confMap.get(item) || StringUtils.isEmpty(confMap.get(item))) {
                conf.set(item, "");
            } else {
                conf.set(item, confMap.get(item));
            }
        });
        return KerberosLoginUtil.loginWithUGI(hdfsSourceDTO.getKerberosConfig(), "").doAs(
                (PrivilegedAction<Boolean>) () -> {
                    Boolean result = false;
                    try {
                        JobClient client = new JobClient(new InetSocketAddress(ip, port), conf);
                        RunningJob job = client.getJob(jobId);

                        log.info("即将停止的job名字为： 【" + job.getJobName() + "】");
                        log.info("即将停止的jobID为： 【" + job.getID() + "】");
                        int jobState = job.getJobState();
                        String stateTypeName = DsgMrJobState.getStateTypeName(jobState);
                        log.info("即将停止的job状态为： 【" + stateTypeName + "】");
                        log.info("开始停止job");
                        if (1 == jobState || 4 == jobState) {
                            job.killJob();
                            //清理job
//                            CustomOutputCommitter customOutputCommitter = new CustomOutputCommitter();
                            JobConf entries = new JobConf(conf);
                            JobContextImpl jobContext = new JobContextImpl(entries, job.getID());
//                            customOutputCommitter.cleanupJob(jobContext);
                            RunningJob stopjob = client.getJob(jobId);
                            int jobState1 = stopjob.getJobState();
                            String stateTypeName1 = DsgMrJobState.getStateTypeName(jobState1);
                            log.info("job状态为： 【" + stateTypeName1 + "】");
                        } else {
                            log.error("job状态为： 【" + stateTypeName + "】");
                            log.error("无法停止状态为" + stateTypeName + "的job");
                        }
                        result = true;
                    } catch (Exception e) {
                        e.printStackTrace();
                        result = false;
                        throw new RuntimeException(e.getMessage());
                    }

                    return result;
                });
    }

    //获取hdfs HA的活跃节点
    @Override
    public String getActiveNameNode(ISourceDTO source) {
        HdfsSourceDTO hdfsSourceDTO = (HdfsSourceDTO) source;
        FileSystem fs = HdfsOperator.getFileSystem(hdfsSourceDTO.getKerberosConfig(), hdfsSourceDTO.getConfig(), hdfsSourceDTO.getDefaultFS());
        String activeNameNode = "";

        try {
            activeNameNode = HAUtil.getAddressOfActive(fs).toString();
        } catch (Exception e) {
            e.printStackTrace();
        }

        return activeNameNode;
    }


    @Override
    public List<HDFSContentSummary> getContentSummary(ISourceDTO source, List<String> hdfsDirPaths) {
        if (CollectionUtils.isEmpty(hdfsDirPaths)) {
            throw new DtLoaderException("hdfs path cannot be empty！");
        }
        HdfsSourceDTO hdfsSourceDTO = (HdfsSourceDTO) source;
        List<HDFSContentSummary> hdfsContentSummaries = Lists.newArrayList();
        // kerberos认证
        return KerberosLoginUtil.loginWithUGI(hdfsSourceDTO.getKerberosConfig()).doAs(
                (PrivilegedAction<List<HDFSContentSummary>>) () -> {
                    try {
                        Configuration conf = HadoopConfUtil.getHdfsConf(hdfsSourceDTO.getDefaultFS(), hdfsSourceDTO.getConfig(), hdfsSourceDTO.getKerberosConfig());
                        FileSystem fs = FileSystem.get(conf);
                        for (String HDFSDirPath : hdfsDirPaths) {
                            Path hdfsPath = new Path(HDFSDirPath);
                            // 判断路径是否存在，不存在则返回空对象
                            HDFSContentSummary hdfsContentSummary;
                            if (!fs.exists(hdfsPath)) {
                                log.warn("execute method getContentSummary: path {} not exists!", HDFSDirPath);
                                hdfsContentSummary = HDFSContentSummary.builder()
                                        .directoryCount(0L)
                                        .fileCount(0L)
                                        .ModifyTime(0L)
                                        .spaceConsumed(0L)
                                        .build();
                                if (ReflectUtil.fieldExists(hdfsContentSummary.getClass(), "isExists")) {
                                    hdfsContentSummary.setIsExists(false);
                                }
                            } else {
                                org.apache.hadoop.fs.FileStatus fileStatus = fs.getFileStatus(hdfsPath);
                                ContentSummary contentSummary = fs.getContentSummary(hdfsPath);
                                hdfsContentSummary = HDFSContentSummary.builder()
                                        .directoryCount(contentSummary.getDirectoryCount())
                                        .fileCount(contentSummary.getFileCount())
                                        .ModifyTime(fileStatus.getModificationTime())
                                        .spaceConsumed(contentSummary.getLength()).build();
                                if (ReflectUtil.fieldExists(hdfsContentSummary.getClass(), "isExists")) {
                                    hdfsContentSummary.setIsExists(true);
                                }
                            }
                            hdfsContentSummaries.add(hdfsContentSummary);
                        }
                        return hdfsContentSummaries;
                    } catch (Exception e) {
                        throw new DtLoaderException(String.format("Failed to obtain HDFS file information：%s", e.getMessage()), e);
                    }
                }
        );
    }

    private int writeByPosWithFileFormat(ISourceDTO source, HdfsWriterDTO hdfsWriterDTO) throws IOException {
        if (FileFormat.ORC.getVal().equals(hdfsWriterDTO.getFileFormat())) {
            return HdfsOrcWriter.writeByPos(source, hdfsWriterDTO);
        }
        if (FileFormat.PARQUET.getVal().equals(hdfsWriterDTO.getFileFormat())) {
            return HdfsParquetWriter.writeByPos(source, hdfsWriterDTO);
        }
        if (FileFormat.TEXT.getVal().equals(hdfsWriterDTO.getFileFormat())) {
            return HdfsTextWriter.writeByPos(source, hdfsWriterDTO);
        }
        throw new DtLoaderException("This storage type file is not supported for writing to hdfs");
    }

    private int writeByNameWithFileFormat(ISourceDTO source, HdfsWriterDTO hdfsWriterDTO) throws IOException {
        if (FileFormat.ORC.getVal().equals(hdfsWriterDTO.getFileFormat())) {
            return HdfsOrcWriter.writeByName(source, hdfsWriterDTO);
        }
        if (FileFormat.PARQUET.getVal().equals(hdfsWriterDTO.getFileFormat())) {
            return HdfsParquetWriter.writeByName(source, hdfsWriterDTO);
        }
        if (FileFormat.TEXT.getVal().equals(hdfsWriterDTO.getFileFormat())) {
            return HdfsTextWriter.writeByName(source, hdfsWriterDTO);
        }
        throw new DtLoaderException("This storage type file is not supported for writing to hdfs");
    }

    private List<ColumnMetaDTO> getColumnListOnFileFormat(HdfsSourceDTO hdfsSourceDTO, SqlQueryDTO queryDTO, String
            fileFormat) throws IOException {

        if (FileFormat.ORC.getVal().equals(fileFormat)) {
            return getOrcColumnList(hdfsSourceDTO, queryDTO);
        }

        throw new DtLoaderException("The file field information acquisition of this storage type is not supported");
    }

    private List<ColumnMetaDTO> getOrcColumnList(HdfsSourceDTO hdfsSourceDTO, SqlQueryDTO queryDTO) throws IOException {
        ArrayList<ColumnMetaDTO> columnList = new ArrayList<>();
        Configuration conf = HdfsOperator.getConfig(hdfsSourceDTO.getKerberosConfig(), hdfsSourceDTO.getConfig(), hdfsSourceDTO.getDefaultFS());
        FileSystem fs = HdfsOperator.getFileSystem(hdfsSourceDTO.getKerberosConfig(), hdfsSourceDTO.getConfig(), hdfsSourceDTO.getDefaultFS());
        OrcFile.ReaderOptions readerOptions = OrcFile.readerOptions(conf);
        readerOptions.filesystem(fs);
        String fileName = hdfsSourceDTO.getDefaultFS() + PATH_DELIMITER + queryDTO.getTableName();
        fileName = handleVariable(fileName);

        Path path = new Path(fileName);
        org.apache.hadoop.hive.ql.io.orc.Reader reader = null;
        String typeStruct = null;
        if (fs.isDirectory(path)) {
            RemoteIterator<LocatedFileStatus> iterator = fs.listFiles(path, true);
            while (iterator.hasNext()) {
                org.apache.hadoop.fs.FileStatus fileStatus = iterator.next();
                if (fileStatus.isFile() && fileStatus.getLen() > 49) {
                    Path subPath = fileStatus.getPath();
                    reader = OrcFile.createReader(subPath, readerOptions);
                    typeStruct = reader.getObjectInspector().getTypeName();
                    if (StringUtils.isNotEmpty(typeStruct)) {
                        break;
                    }
                }
            }
            if (reader == null) {
                throw new DtLoaderException("orcfile dir is empty!");
            }

        } else {
            reader = OrcFile.createReader(path, readerOptions);
            typeStruct = reader.getObjectInspector().getTypeName();
        }

        if (StringUtils.isEmpty(typeStruct)) {
            throw new DtLoaderException("can't retrieve type struct from " + path);
        }

        int startIndex = typeStruct.indexOf("<") + 1;
        int endIndex = typeStruct.lastIndexOf(">");
        typeStruct = typeStruct.substring(startIndex, endIndex);
        List<String> cols = StringUtil.splitIgnoreQuota(typeStruct, ',');
        for (String col : cols) {
            List<String> colNameAndType = StringUtil.splitIgnoreQuota(col, ':');
            if (CollectionUtils.isEmpty(colNameAndType) || colNameAndType.size() != 2) {
                continue;
            }
            ColumnMetaDTO metaDTO = new ColumnMetaDTO();
            metaDTO.setKey(colNameAndType.get(0));
            metaDTO.setType(colNameAndType.get(1));
            columnList.add(metaDTO);
        }
        return columnList;
    }

    private static String handleVariable(String path) {
        if (path.endsWith(PATH_DELIMITER)) {
            path = path.substring(0, path.length() - 1);
        }

        int pos = path.lastIndexOf(PATH_DELIMITER);
        String file = path.substring(pos + 1, path.length());

        if (file.matches(".*\\$\\{.*\\}.*")) {
            return path.substring(0, pos);
        }

        return path;
    }

    private List<FileStatus> listFiles(FileSystem fs, String remotePath, boolean isIterate) {
        try {
            return transferFileStatus(HdfsOperator.listFiles(fs, remotePath, isIterate));
        } catch (IOException e) {
            throw new DtLoaderException(String.format("Failed to get the file in the target path : %s", e.getMessage()), e);
        }
    }

    /**
     * Apache Status 转换
     *
     * @param fileStatuses
     * @return
     */
    private List<FileStatus> transferFileStatus(List<org.apache.hadoop.fs.FileStatus> fileStatuses) {
        List<FileStatus> fileStatusList = new ArrayList<>();
        for (org.apache.hadoop.fs.FileStatus fileStatus : fileStatuses) {
            FileStatus fileStatusTemp = FileStatus.builder()
                    .length(fileStatus.getLen())
                    .access_time(fileStatus.getAccessTime())
                    .block_replication(fileStatus.getReplication())
                    .blocksize(fileStatus.getBlockSize())
                    .group(fileStatus.getGroup())
                    .isdir(fileStatus.isDirectory())
                    .modification_time(fileStatus.getModificationTime())
                    .owner(fileStatus.getOwner())
                    .path(fileStatus.getPath().toString())
                    .build();
            fileStatusList.add(fileStatusTemp);
        }
        return fileStatusList;
    }
}
