package com.dtstack.dtcenter.common.loader.saphana;

import com.dsg.database.datasource.vo.DbTableVO;
import com.dtstack.dtcenter.common.loader.common.exception.ErrorCode;
import com.dtstack.dtcenter.common.loader.common.utils.DBUtil;
import com.dtstack.dtcenter.common.loader.common.utils.SchemaUtil;
import com.dtstack.dtcenter.common.loader.rdbms.AbsRdbmsClient;
import com.dtstack.dtcenter.common.loader.rdbms.ConnFactory;
import com.dtstack.dtcenter.loader.IDownloader;
import com.dtstack.dtcenter.loader.dto.ColumnMetaDTO;
import com.dtstack.dtcenter.loader.dto.SqlQueryDTO;
import com.dtstack.dtcenter.loader.dto.source.*;
import com.dtstack.dtcenter.loader.exception.DtLoaderException;
import com.dtstack.dtcenter.loader.source.DataSourceType;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;

import java.sql.*;
import java.util.*;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * sap hana 客户端，获取表需要有 SYS schema 的权限
 *
 * <AUTHOR>
 * date：Created in 上午10:13 2021/12/30
 * company: www.dtstack.com
 */
@Slf4j
public class SapHanaClient extends AbsRdbmsClient {

    // 获取正在使用 schema
    private static final String CURRENT_DB = " SELECT CURRENT_SCHEMA FROM DUMMY ";

    // schema 是否存在
    private static final String DB_EXISTS = " SELECT * FROM SYS.SCHEMAS WHERE SCHEMA_NAME = '%s' ";

    // 查询表注释
    private static final String TABLE_COMMENT = " SELECT COMMENTS FROM SYS.TABLES WHERE SCHEMA_NAME = '%s' AND TABLE_NAME = '%s' LIMIT 1 ";

    // 查询表基本 sql
    private static final String SHOW_TABLE_BASE = " SELECT TABLE_NAME, TABLE_NAME_UPPER FROM (%s) WHERE 1 = 1 ";

    // 查询视图基本 sql
    private static final String SHOW_VIEW_BASE = " SELECT VIEW_NAME, VIEW_NAME_UPPER FROM (%s)  WHERE 1 = 1 ";

    // 获取指定数据库下的表
    private static final String SHOW_TABLE_BY_SCHEMA_SQL = " SELECT UPPER(TABLE_NAME) AS TABLE_NAME_UPPER, TABLE_NAME, SCHEMA_NAME FROM SYS.TABLES WHERE SCHEMA_NAME = '%s' ";

    // 获取全部表
    private static final String SHOW_SCHEMA_TABLE_SQL = " SELECT UPPER('\"'||SCHEMA_NAME||'\".\"'||TABLE_NAME||'\"') AS TABLE_NAME_UPPER, '\"'||SCHEMA_NAME||'\".\"'||TABLE_NAME||'\"' AS TABLE_NAME FROM SYS.TABLES ";

    // 获取全部视图
    private static final String SHOW_SCHEMA_VIEW_SQL = " SELECT UPPER('\"'||SCHEMA_NAME||'\".\"'||VIEW_NAME||'\"') AS VIEW_NAME_UPPER, '\"'||SCHEMA_NAME||'\".\"'||VIEW_NAME||'\"' AS VIEW_NAME FROM SYS.VIEWS ";

    // 获取指定数据库下的视图
    private static final String SHOW_VIEW_BY_SCHEMA_SQL = " SELECT UPPER(VIEW_NAME) AS VIEW_NAME_UPPER, VIEW_NAME, SCHEMA_NAME FROM SYS.VIEWS WHERE SCHEMA_NAME = '%s' ";

    // 表名模糊查询
    private static final String TABLE_SEARCH_SQL = " AND TABLE_NAME_UPPER LIKE UPPER('%s') ";

    // 视图模糊查询
    private static final String VIEW_SEARCH_SQL = " AND VIEW_NAME_UPPER LIKE UPPER('%s') ";

    // 限制条数语句
    private static final String LIMIT_SQL = " LIMIT %s ";

    // 创建数据库
    private static final String CREATE_SCHEMA_SQL_TMPL = " CREATE SCHEMA %s ";

    // 判断表是否在指定 schema 中
    private static final String TABLE_IS_IN_SCHEMA = " SELECT TABLE_NAME from SYS.TABLES WHERE SCHEMA_NAME = '%s' AND TABLE_NAME = '%s' ";

    // 判断视图是否在指定 schema 中
    private static final String VIEW_IS_IN_SCHEMA = " SELECT VIEW_NAME from SYS.VIEWS WHERE SCHEMA_NAME = '%s' AND VIEW_NAME = '%s' ";

    // 获取所有的 schema
    private static final String SHOW_SCHEMA = " SELECT SCHEMA_NAME FROM SYS.SCHEMAS ";

    @Override
    protected ConnFactory getConnFactory() {
        return new SapHanaConnFactory();
    }

    @Override
    protected DataSourceType getSourceType() {
        return DataSourceType.SAP_HANA1;
    }

    @Override
    public List<String> getTableList(ISourceDTO sourceDTO, SqlQueryDTO queryDTO) {
        return getTableListBySchema(sourceDTO, queryDTO);
    }

    @Override
    public String getTableMetaComment(ISourceDTO sourceDTO, SqlQueryDTO queryDTO) {
        String schema = getSchema(sourceDTO, queryDTO);
        // 构建查询条件
        SqlQueryDTO customQueryDTO = SqlQueryDTO.builder()
                .sql(String.format(TABLE_COMMENT, schema, queryDTO.getTableName()))
                .build();

        // 查询表注释信息
        List<Map<String, Object>> result = executeQuery(sourceDTO, customQueryDTO);
        if (CollectionUtils.isEmpty(result)) {
            return StringUtils.EMPTY;
        }
        return result
                .stream().findAny().map(Map::values).orElse(Collections.emptyList())
                .stream().filter(Objects::nonNull).findAny().orElse(StringUtils.EMPTY)
                .toString();

    }

    @Override
    public IDownloader getDownloader(ISourceDTO sourceDTO, SqlQueryDTO queryDTO) throws Exception {
        SapHana1SourceDTO sapHanaSourceDTO = (SapHana1SourceDTO) sourceDTO;
        SapHanaDownloader sapHanaDownloader = new SapHanaDownloader(getCon(sourceDTO), queryDTO.getSql(), sapHanaSourceDTO.getSchema());
        sapHanaDownloader.configure();
        return sapHanaDownloader;
    }

    @Override
    protected String getCurrentDbSql() {
        return CURRENT_DB;
    }

    @Override
    protected String getCreateDatabaseSql(String dbName, String comment) {
        return String.format(CREATE_SCHEMA_SQL_TMPL, dbName);
    }

    @Override
    public Boolean isDatabaseExists(ISourceDTO source, String dbName) {
        if (StringUtils.isBlank(dbName)) {
            throw new DtLoaderException("database name is not empty");
        }
        return CollectionUtils.isNotEmpty(executeQuery(source, SqlQueryDTO.builder().sql(String.format(DB_EXISTS, dbName)).build()));
    }

    @Override
    public Boolean isTableExistsInDatabase(ISourceDTO source, SqlQueryDTO queryDTO) {
        if (StringUtils.isBlank(queryDTO.getDbName())) {
            throw new DtLoaderException("database name is not empty");
        }
        String executeSql = String.format(TABLE_IS_IN_SCHEMA, queryDTO.getDbName(), queryDTO.getTableName())
                + " UNION "
                + String.format(VIEW_IS_IN_SCHEMA, queryDTO.getDbName(), queryDTO.getTableName());
        return CollectionUtils.isNotEmpty(executeQuery(source, SqlQueryDTO.builder().sql(executeSql).build()));
    }

    /**
     * 获取指定schema下的表，如果没有填schema，默认使用当前schema。支持正则匹配查询、条数限制
     *
     * @param sourceDTO 数据源信息
     * @param queryDTO  查询条件
     * @return 拼装后的 sql
     */
    @Override
    protected String getTableBySchemaSql(ISourceDTO sourceDTO, SqlQueryDTO queryDTO) {

        // 判断 schema 是否存在, 不传 schema 获取所有表
        String schema = SchemaUtil.getSchema(sourceDTO, queryDTO);
        log.info("current used schema：{}", schema);

        // 最终查询的 sql
        StringBuilder querySql = new StringBuilder();

        // 条件查询表
        String showTable;
        if (StringUtils.isBlank(schema)) {
            showTable = SHOW_SCHEMA_TABLE_SQL;
        } else {
            showTable = String.format(SHOW_TABLE_BY_SCHEMA_SQL, schema);
        }
        querySql.append(String.format(SHOW_TABLE_BASE, showTable));
        // 拼接模糊查询, 忽略大小写
        if (StringUtils.isNotBlank(queryDTO.getTableNamePattern())) {
            querySql.append(String.format(TABLE_SEARCH_SQL, addFuzzySign(queryDTO)));
        }

        if (BooleanUtils.isTrue(queryDTO.getView())) {

            // 拼接 UNION
            querySql.append(" UNION ");

            // 条件查询视图
            String showView;
            if (StringUtils.isBlank(schema)) {
                showView = SHOW_SCHEMA_VIEW_SQL;
            } else {
                showView = String.format(SHOW_VIEW_BY_SCHEMA_SQL, schema);
            }
            querySql.append(String.format(SHOW_VIEW_BASE, showView));

            // 拼接模糊查询, 忽略大小写
            if (StringUtils.isNotBlank(queryDTO.getTableNamePattern())) {
                querySql.append(String.format(VIEW_SEARCH_SQL, addFuzzySign(queryDTO)));
            }

        }
        if (Objects.nonNull(queryDTO.getLimit())) {
            querySql.append(String.format(LIMIT_SQL, queryDTO.getLimit()));
        }
        return querySql.toString();
    }

    /**
     * 处理 schema和tableName，适配schema和tableName中有.的情况
     *
     * @param schema    schema 名称
     * @param tableName 表名称
     * @return 处理后的 schema tableName 格式
     */
    @Override
    protected String transferSchemaAndTableName(String schema, String tableName) {
        if (!tableName.startsWith("\"") || !tableName.endsWith("\"")) {
            tableName = String.format("\"%s\"", tableName);
        }
        if (StringUtils.isBlank(schema)) {
            return tableName;
        }
        if (!schema.startsWith("\"") || !schema.endsWith("\"")) {
            schema = String.format("\"%s\"", schema);
        }
        return String.format("%s.%s", schema, tableName);
    }

    /**
     * 获取 schema, 入参中获取不到则获取当前使用的 schema
     *
     * @param sourceDTO   数据源连接信息
     * @param sqlQueryDTO 查询条件
     * @return schema 信息
     */
    public String getSchema(ISourceDTO sourceDTO, SqlQueryDTO sqlQueryDTO) {
        String inputSchema = SchemaUtil.getSchema(sourceDTO, sqlQueryDTO);
        return StringUtils.isBlank(inputSchema) ? getCurrentDatabase(sourceDTO) : inputSchema;
    }

    @Override
    protected String getShowDbSql() {
        return SHOW_SCHEMA;
    }

    @Override
    protected String getDbsSql() {
        return SHOW_SCHEMA;
    }
    @Override
    public List<String> getAllDbs(ISourceDTO source, SqlQueryDTO queryDTO) {
        // 获取表信息需要通过show databases 语句
        String sql = getDbsSql();
        List getAllDatabaseException = queryWithSingleColumn(source, null, sql, 1, "get All database exception");
        //排除调系统库
        if(CollectionUtils.isNotEmpty(getAllDatabaseException)){
            getAllDatabaseException=filterSchemas(getAllDatabaseException);
        }
        return getAllDatabaseException;
    }

    /**
     * 过滤掉名称为 SYS_DATABASES 和以 _SYS 开头的 schema
     *
     * @param schemaList 原始 schema 列表
     * @return 过滤后的 schema 列表
     */
    public static List<String> filterSchemas(List<String> schemaList) {
        return schemaList.stream()
                .filter(schema ->
                        !"SYS_DATABASES".equalsIgnoreCase(schema) &&
                                !schema.toUpperCase().startsWith("_SYS")
                )
                .collect(Collectors.toList());
    }


    @Override
    public String getCreateTableSql(ISourceDTO source, SqlQueryDTO queryDTO) {
        if(SqlConstants.TABLE_TYPE.equalsIgnoreCase(queryDTO.getType())){
            Integer clearStatus = beforeColumnQuery(source, queryDTO);
            SapHana1SourceDTO sapHanaSourceDTO = (SapHana1SourceDTO) source;
            try {
                String ddl = generateCreateTableDDL(sapHanaSourceDTO.getConnection(), queryDTO.getDbName(), queryDTO.getTableName());
                return ddl;
            }catch (Exception e){
                log.info("getDdl异常{}",e.getMessage());
                return "";
            }
        }

        String createTableSql="";
        if(SqlConstants.FUNCTION_TYPE.equalsIgnoreCase(queryDTO.getType())){
            createTableSql = String.format(SqlConstants.FUNCTION_CREATE_SQL, queryDTO.getDbName(), queryDTO.getTableName());
        }
        if(SqlConstants.PROCEDURE_TYPE.equalsIgnoreCase(queryDTO.getType())){
            createTableSql = String.format(SqlConstants.PROCEDURE_CREATE_SQL, queryDTO.getDbName(), queryDTO.getTableName());
        }
        queryDTO.setSql(createTableSql);
        return super.getCreateTableSql(source, queryDTO);
    }

    public static String generateCreateTableDDL(Connection conn, String schema, String tableName) throws SQLException {
        String columnSql = "SELECT COLUMN_NAME, DATA_TYPE_NAME, LENGTH, SCALE, IS_NULLABLE, DEFAULT_VALUE " +
                "FROM SYS.TABLE_COLUMNS " +
                "WHERE SCHEMA_NAME = ? AND TABLE_NAME = ? ORDER BY POSITION";

        StringBuilder ddl = new StringBuilder();
        ddl.append("CREATE COLUMN TABLE \"").append(schema).append("\".\"").append(tableName).append("\" (\n");

        try (PreparedStatement ps = conn.prepareStatement(columnSql)) {
            ps.setString(1, schema);
            ps.setString(2, tableName);

            try (ResultSet rs = ps.executeQuery()) {
                List<String> columnDefs = new ArrayList<>();

                while (rs.next()) {
                    String columnName = rs.getString("COLUMN_NAME");
                    String dataType = rs.getString("DATA_TYPE_NAME");
                    int length = rs.getInt("LENGTH");
                    int scale = rs.getInt("SCALE");
                    boolean isNullable = rs.getString("IS_NULLABLE").equalsIgnoreCase("TRUE");
                    String defaultValue = rs.getString("DEFAULT_VALUE");

                    StringBuilder colDef = new StringBuilder();
                    colDef.append("  \"").append(columnName).append("\" ").append(dataType);

                    // 判断是否带长度或精度
                    if (Arrays.asList("VARCHAR", "NVARCHAR", "VARBINARY").contains(dataType) && length > 0) {
                        colDef.append("(").append(length).append(")");
                    } else if (Arrays.asList("DECIMAL", "DEC", "NUMERIC").contains(dataType) && length > 0) {
                        colDef.append("(").append(length);
                        if (scale > 0) {
                            colDef.append(",").append(scale);
                        }
                        colDef.append(")");
                    }

                    if (!isNullable) {
                        colDef.append(" NOT NULL");
                    }

                    if (defaultValue != null && !defaultValue.trim().isEmpty()) {
                        colDef.append(" DEFAULT ").append(defaultValue);
                    }

                    columnDefs.add(colDef.toString());
                }

                ddl.append(String.join(",\n", columnDefs)).append("\n);\n");
            }
        }

        return ddl.toString();
    }


    @Override
    public List<List<Object>> getPreview(ISourceDTO iSource, SqlQueryDTO queryDTO) {
        Integer clearStatus = beforeColumnQuery(iSource, queryDTO);
        SapHana1SourceDTO sapHanaSourceDTO = (SapHana1SourceDTO) iSource;
        List<List<Object>> previewList = new ArrayList<>();
        if (StringUtils.isBlank(queryDTO.getTableName())) {
            return previewList;
        }
        Statement stmt = null;
        ResultSet rs = null;
        PreparedStatement preparedStatement = null;
        try {

            stmt = sapHanaSourceDTO.getConnection().createStatement(ResultSet.TYPE_FORWARD_ONLY, ResultSet.CONCUR_READ_ONLY);
            //查询sql，默认预览100条
            String querySql = dealSql(sapHanaSourceDTO, queryDTO);
            String whereSql = dealWhereSql(sapHanaSourceDTO, querySql, queryDTO);
            querySql = querySql + whereSql;
            if (queryDTO.getPreviewNum() != null) {
                int endIndex = queryDTO.getPreviewNum();
                Integer pageNum = queryDTO.getPageNum();
                if (pageNum != null) {
                    endIndex = pageNum * endIndex;
                }
                stmt.setMaxRows(endIndex);
            }
            rs = stmt.executeQuery(querySql);

            if (queryDTO.getPreviewNum() != null) {
                int previewNum = queryDTO.getPreviewNum();
                Integer pageNum = queryDTO.getPageNum();
                if (pageNum != null) {
                    // 查询分页数据
                    int beginIndex = (pageNum - 1) * previewNum;
                    if (beginIndex > 0) {
                        // 设置读取数据的起始位置
                        rs.absolute(beginIndex);
                    }
                }
            }


            ResultSetMetaData rsmd = rs.getMetaData();
            //存储字段信息
            List<Object> metaDataList = Lists.newArrayList();
            //字段数量
            int len = rsmd.getColumnCount();
            for (int i = 0; i < len; i++) {
                metaDataList.add(rsmd.getColumnLabel(i + 1));
            }
            previewList.add(metaDataList);
            while (rs.next()) {
                //一个columnData存储一行数据信息
                ArrayList<Object> columnData = Lists.newArrayList();
                for (int i = 0; i < len; i++) {
                    String result = dealPreviewResult(rs.getObject(i + 1));
                    columnData.add(result);
                }
                previewList.add(columnData);
            }
        } catch (Exception e) {
            throw new DtLoaderException(e.getMessage(), e);
        } finally {
            DBUtil.closeDBResources(rs, stmt, DBUtil.clearAfterGetConnection(sapHanaSourceDTO, clearStatus));
            if (preparedStatement != null) {
                try {
                    preparedStatement.close();
                } catch (SQLException e) {
                    e.printStackTrace();
                }
            }
        }
        return previewList;
    }

    @Override
    public int getPreviewRows(ISourceDTO source, SqlQueryDTO queryDTO) {
        Integer clearStatus = beforeQuery(source, queryDTO, false);
        SapHana1SourceDTO sapHanaSourceDTO = (SapHana1SourceDTO) source;
        Statement statement = null;
        ResultSet rs = null;
        int count = 0;
        try {
            statement = sapHanaSourceDTO.getConnection().createStatement(ResultSet.TYPE_FORWARD_ONLY, ResultSet.CONCUR_READ_ONLY);
            //查询sql，默认预览100条
            String querySql = dealCountPreviewSql(sapHanaSourceDTO, queryDTO);
            String whereSql = dealWhereSql(sapHanaSourceDTO, querySql, queryDTO);
            querySql = querySql + whereSql;
            statement = sapHanaSourceDTO.getConnection().createStatement();
            rs = statement.executeQuery(querySql);
            while (rs.next()) {
                count = rs.getInt(1);
                break;
            }
            try {
                statement.executeQuery(String.format("ANALYZE TABLE %s COMPUTE STATISTICS", transferSchemaAndTableName(source, queryDTO)));
            } catch (Exception e) {
                log.warn("需要刷新元数据异常，设置count为0", e);
            }
            rs = statement.executeQuery(querySql);
            while (rs.next()) {
                count = rs.getInt(1);
                break;
            }

        } catch (Exception e) {
            throw new DtLoaderException(String.format("Get preview count exception：%s", e.getMessage()), e);
        } finally {
            DBUtil.closeDBResources(rs, statement, DBUtil.clearAfterGetConnection(sapHanaSourceDTO, clearStatus));
        }
        return count;
    }


    @Override
    public Integer getMaxConnections(ISourceDTO sourceDTO) {
        Integer result = null;
        try {
            List<Map<String, Object>> resultList = executeQuery(sourceDTO, SqlQueryDTO.builder().sql(SqlConstants.GET_MAX_CONNECTIONS).build());
            if (CollectionUtils.isNotEmpty(resultList)) {
                result = MapUtils.getInteger(resultList.get(0), "MAX_CONNECTIONS");
            }
        } catch (Exception e) {
            log.error("get max connections error", e);
        }
        return result;
    }

    @Override
    public Boolean getMetadataPrivileges(ISourceDTO sourceDTO) {
        Boolean result = null;
        SapHana1SourceDTO sapHana1SourceDTO = (SapHana1SourceDTO) sourceDTO;
        Integer clearStatus = beforeQuery(sourceDTO, SqlQueryDTO.builder().build(), false);
        try {
            result = checkViaGrants(sapHana1SourceDTO.getConnection()) || checkViaTestQuery(sapHana1SourceDTO.getConnection());
        } catch (Exception e) {
            // 异常为无权限
            result = false;
        } finally {
            DBUtil.closeDBResources(null, null, DBUtil.clearAfterGetConnection(sapHana1SourceDTO, clearStatus));
        }
        return result;
    }

    private boolean checkViaGrants(Connection conn) throws SQLException {
        try (Statement stmt = conn.createStatement(); ResultSet rs = stmt.executeQuery(SqlConstants.SHOW_GRANTS)) {
            Pattern globalSelectPattern = Pattern.compile(
                    "^GRANT .*\\bSELECT\\b.* ON \\*\\.\\* TO ",
                    Pattern.CASE_INSENSITIVE
            );
            while (rs.next()) {
                String grant = rs.getString(1);
                // 匹配是否存在全局 SELECT 权限
                if (globalSelectPattern.matcher(grant).find()) {
                    return true;
                }
            }
        }
        return false;
    }

    private boolean checkViaTestQuery(Connection conn) {
        try (Statement stmt = conn.createStatement();
             ResultSet rs = stmt.executeQuery(SqlConstants.GET_METADATA_PRIVILEGES)) {
            // 查询成功 = 有权限
            return rs.next();
        } catch (SQLException e) {
            return false; // 查询失败 = 无权限
        }
    }

    /**
     * 获取表
     *
     * @param sourceDTO 数据源描述对象，包含连接数据库所需的信息，如数据库类型、连接字符串等
     * @param queryDTO  SQL查询对象，包含查询数据库表所需的条件，如表名、schema名等
     * @return
     */
    @Override
    public List<DbTableVO> getMedataDataTables(ISourceDTO sourceDTO, SqlQueryDTO queryDTO) {
        SapHana1SourceDTO sapHana1SourceDTO = (SapHana1SourceDTO) sourceDTO;
        Integer clearStatus = beforeQuery(sourceDTO, queryDTO, false);
        Statement statement = null;
        ResultSet resultSet = null;
        List<DbTableVO> dbTableVOS = new ArrayList<>();
        try {
            statement = sapHana1SourceDTO.getConnection().createStatement();

            //判断搜查类型为空时查全部
            String sql = "";
            String type = SqlConstants.BASE_TABLE;
            sql = String.format(SqlConstants.GET_TABLE_SCHEMA_SQL, queryDTO.getDbName());
            if (SqlConstants.VIEW_TYPE.equalsIgnoreCase(queryDTO.getType())) {
                sql = String.format(SqlConstants.GET_VIEW_SCHEMA_SQL, queryDTO.getDbName());
            }

            if (StringUtils.isNotEmpty(queryDTO.getTableNamePattern())) {
                String format = String.format(SqlConstants.SEARCH_SQL, queryDTO.getTableNamePattern());
                sql = sql + format;
            }

            log.info("getMedataDataTables sql:{}", sql);
            resultSet = statement.executeQuery(sql);
            while (resultSet.next()) {
                DbTableVO dbTableVO = new DbTableVO();
                dbTableVO.setDbName(resultSet.getString("SCHEMA_NAME"));
                dbTableVO.setName(resultSet.getString("NAME"));
                dbTableVO.setType(queryDTO.getType());
                dbTableVOS.add(dbTableVO);
            }
        } catch (Exception e) {
            throw new DtLoaderException(String.format("根据指定库获取表或者视图异常:{}"), e);
        } finally {
            DBUtil.closeDBResources(resultSet, statement, DBUtil.clearAfterGetConnection(sapHana1SourceDTO, clearStatus));
        }
        return dbTableVOS;
    }

    /**
     * 获取索引信息
     *
     * @param sourceDTO
     * @param queryDTO
     * @return
     */
    @Override
    public List<DbTableVO> getIndexList(ISourceDTO sourceDTO, SqlQueryDTO queryDTO) {
        SapHana1SourceDTO sapHana1SourceDTO = (SapHana1SourceDTO) sourceDTO;
        Integer clearStatus = beforeQuery(sourceDTO, queryDTO, false);
        Statement statement = null;
        ResultSet resultSet = null;
        List<DbTableVO> indexMetaDTOS = new ArrayList<>();
        try {
            statement = sapHana1SourceDTO.getConnection().createStatement();
            //判断搜查类型为空时查全部
            String sql = "";
            //表名为空时  查所有
            if (StringUtils.isEmpty(queryDTO.getTableName())) {
                sql = String.format(SqlConstants.GET_INDEX_SQL, queryDTO.getDbName());
            } else {
                sql = String.format(SqlConstants.GET_INDEX_SQL_BY_TABLE, queryDTO.getDbName(), queryDTO.getTableName());
            }
            //是否有模糊搜索
            if (StringUtils.isNotEmpty(queryDTO.getTableNamePattern())) {
                String format = String.format(SqlConstants.SEARCH_INDEX_SQL, queryDTO.getTableNamePattern());
                sql = sql + format;
            }
            log.info("getIndexList SQl:" + sql);
            resultSet = statement.executeQuery(sql);
            while (resultSet.next()) {
                DbTableVO indexMetaDTO = new DbTableVO();
                indexMetaDTO.setDbName(resultSet.getString("TABLE_SCHEMA"));
                indexMetaDTO.setTableName(resultSet.getString("TABLE_NAME"));
                indexMetaDTO.setName(resultSet.getString("INDEX_NAME"));
                String indexType = resultSet.getString("INDEX_TYPE");
                indexMetaDTO.setIndexType(indexType);
                indexMetaDTO.setType(queryDTO.getType());
                indexMetaDTOS.add(indexMetaDTO);
            }
        } catch (Exception e) {
            throw new DtLoaderException(String.format("根据指定库获取索引异常:{}"), e);
        } finally {
            DBUtil.closeDBResources(resultSet, statement, DBUtil.clearAfterGetConnection(sapHana1SourceDTO, clearStatus));
        }
        return indexMetaDTOS;
    }

    @Override
    public List<ColumnMetaDTO> getIndexColumn(ISourceDTO sourceDTO, SqlQueryDTO queryDTO) {
        SapHana1SourceDTO sapHana1SourceDTO = (SapHana1SourceDTO) sourceDTO;
        Integer clearStatus = beforeQuery(sourceDTO, queryDTO, false);
        Statement statement = null;
        ResultSet resultSet = null;
        List<ColumnMetaDTO> indexCols = new ArrayList<>();
        try {
            statement = sapHana1SourceDTO.getConnection().createStatement();
            //判断搜查类型为空时查全部
            String sql = String.format(SqlConstants.GET_INDEX_COLUMN_SQL, queryDTO.getDbName(), queryDTO.getTableName(), queryDTO.getIndexName());
            resultSet = statement.executeQuery(sql);
            while (resultSet.next()) {
                ColumnMetaDTO indexMetaDTO = new ColumnMetaDTO();
                indexMetaDTO.setKey(resultSet.getString("COLUMN_NAME"));
                indexMetaDTO.setColumnOrder(resultSet.getInt("SEQ_IN_INDEX"));
                boolean collation = resultSet.getBoolean("collation");
                if (Boolean.TRUE.equals(collation)) {
                    indexMetaDTO.setCollation("ASC");
                }else {
                    indexMetaDTO.setCollation("DESC");
                }
                indexCols.add(indexMetaDTO);
            }
        } catch (Exception e) {
            throw new DtLoaderException(String.format("根据指定库获取索引字段异常:{}"), e);
        } finally {
            DBUtil.closeDBResources(resultSet, statement, DBUtil.clearAfterGetConnection(sapHana1SourceDTO, clearStatus));
        }
        return indexCols;
    }
    @Override
    public List<DbTableVO> getFunctionList(ISourceDTO sourceDTO, SqlQueryDTO queryDTO) {
        SapHana1SourceDTO sapHana1SourceDTO = (SapHana1SourceDTO) sourceDTO;
        Integer clearStatus = beforeQuery(sourceDTO, queryDTO, false);
        Statement statement = null;
        ResultSet resultSet = null;
        List<DbTableVO> indexMetaDTOS = new ArrayList<>();
        try {
            statement = sapHana1SourceDTO.getConnection().createStatement();
            //判断搜查类型为空时查全部
            String sql = "";
            //type 为函数还是存储过程
            if (SqlConstants.FUNCTION_TYPE.equalsIgnoreCase(queryDTO.getType())) {
                sql = String.format(SqlConstants.GET_FUNCTION_SQL, queryDTO.getDbName());
            }

            if (SqlConstants.PROCEDURE_TYPE.equalsIgnoreCase(queryDTO.getType())) {
                sql = String.format(SqlConstants.GET_PRODUCE_SQL, queryDTO.getDbName());
            }
            if (StringUtils.isNotEmpty(queryDTO.getTableNamePattern())) {
                String format = String.format(SqlConstants.SEARCH_PRODUCE_SQL, queryDTO.getTableNamePattern());
                sql = sql + format;
            }

            log.info("getFunctionList SQl:" + sql);
            resultSet = statement.executeQuery(sql);
            while (resultSet.next()) {
                DbTableVO indexMetaDTO = new DbTableVO();
                indexMetaDTO.setDbName(resultSet.getString("ROUTINE_SCHEMA"));
                indexMetaDTO.setName(resultSet.getString("ROUTINE_NAME"));
                indexMetaDTO.setType(queryDTO.getType());
                indexMetaDTO.setDdl(resultSet.getString("ROUTINE_DEFINITION"));
                indexMetaDTOS.add(indexMetaDTO);
            }
        } catch (Exception e) {
            throw new DtLoaderException(String.format("根据指定库获取函数或者存储过程异常:{}"), e);
        } finally {
            DBUtil.closeDBResources(resultSet, statement, DBUtil.clearAfterGetConnection(sapHana1SourceDTO, clearStatus));
        }
        return indexMetaDTOS;
    }
    @Override
    public List<ColumnMetaDTO> getFunctionArguments(ISourceDTO sourceDTO, SqlQueryDTO queryDTO) {
        SapHana1SourceDTO sapHana1SourceDTO = (SapHana1SourceDTO) sourceDTO;
        Integer clearStatus = beforeQuery(sourceDTO, queryDTO, false);
        Statement statement = null;
        ResultSet resultSet = null;
        List<ColumnMetaDTO> indexCols = new ArrayList<>();
        try {
            statement = sapHana1SourceDTO.getConnection().createStatement();
            //判断搜查类型为空时查全部
            String sql = String.format(SqlConstants.GET_PRODUCE_ARGUMENTS_SQL, queryDTO.getDbName(), queryDTO.getObjectName());
            log.info("getFunctionArguments SQl:" + sql);
            resultSet = statement.executeQuery(sql);
            while (resultSet.next()) {
                ColumnMetaDTO indexMetaDTO = new ColumnMetaDTO();
                String parameterName = resultSet.getString("PARAMETER_NAME");
                if (StringUtils.isEmpty(parameterName)) {
                    parameterName = "RETURN";
                }
                indexMetaDTO.setKey(parameterName);
                indexMetaDTO.setType(resultSet.getString("DATA_TYPE"));
                String parameterMode = resultSet.getString("PARAMETER_MODE");
                if (StringUtils.isEmpty(parameterMode)) {
                    parameterMode = "RETURN";
                }
                indexMetaDTO.setInOut(parameterMode);
                indexMetaDTO.setColumnOrder(resultSet.getInt("ORDINAL_POSITION"));
                int characterOctetLength = resultSet.getInt("CHARACTER_OCTET_LENGTH");
                indexMetaDTO.setScale(resultSet.getInt("NUMERIC_SCALE"));
                if (characterOctetLength > 0) {
                    indexMetaDTO.setLength(characterOctetLength);
                    indexMetaDTO.setPrecision(characterOctetLength);
                }
                boolean isNullable = resultSet.getBoolean("IS_NULLABLE");
                if(isNullable){
                    indexMetaDTO.setNotNullFlag(isNullable);
                }
                indexCols.add(indexMetaDTO);
            }
        } catch (Exception e) {
            throw new DtLoaderException(String.format("根据指定库获取函数或者存储过程异常:{}"), e);
        } finally {
            DBUtil.closeDBResources(resultSet, statement, DBUtil.clearAfterGetConnection(sapHana1SourceDTO, clearStatus));
        }
        return indexCols;
    }

}
