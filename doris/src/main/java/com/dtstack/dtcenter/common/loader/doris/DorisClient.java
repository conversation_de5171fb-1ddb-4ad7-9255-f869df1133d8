/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.dtstack.dtcenter.common.loader.doris;

import com.dsg.database.datasource.enums.DataSourceTypeEnum;
import com.dsg.database.datasource.vo.DbTableVO;
import com.dtstack.dtcenter.common.loader.common.utils.CollectionUtil;
import com.dtstack.dtcenter.common.loader.common.utils.DBUtil;
import com.dtstack.dtcenter.common.loader.mysql5.MysqlClient;
import com.dtstack.dtcenter.common.loader.rdbms.ConnFactory;
import com.dtstack.dtcenter.loader.dto.ColumnMetaDTO;
import com.dtstack.dtcenter.loader.dto.SqlQueryDTO;
import com.dtstack.dtcenter.loader.dto.source.ISourceDTO;
import com.dtstack.dtcenter.loader.dto.source.ImpalaSourceDTO;
import com.dtstack.dtcenter.loader.dto.source.Mysql5SourceDTO;
import com.dtstack.dtcenter.loader.dto.source.RdbmsSourceDTO;
import com.dtstack.dtcenter.loader.exception.DtLoaderException;
import com.dtstack.dtcenter.loader.source.DataSourceType;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;

import java.sql.*;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 * date：Created in 下午1:46 2021/07/09
 * company: www.dtstack.com
 */
@Slf4j
public class DorisClient extends MysqlClient {
    /**
     * 查询的格式: default_cluster:dbName
     *
     * @param source
     * @return
     */
    private static final String DONT_EXIST = "doesn't exist";

    private static final Map<Short, String> indexTypeMap = new HashMap<Short, String>() {
        {
            put((short) 0, "tableIndexStatistic");
            put((short) 1, "tableIndexClustered");
            put((short) 2, "tableIndexHashed");
            put((short) 3, "tableIndexOther");
        }
    };
    @Override
    public String getCurrentDatabase(ISourceDTO source) {
        String currentDb = super.getCurrentDatabase(source);
        return currentDb.substring(currentDb.lastIndexOf(":") + 1);
    }

    @Override
    protected ConnFactory getConnFactory() {
        return new DorisConnFactory();
    }

    @Override
    protected DataSourceType getSourceType() {
        return DataSourceType.DORIS;
    }


    @Override
    protected String transferSchemaAndTableName(String schema, String tableName) {
        if (StringUtils.isBlank(schema)) {
            //DataSourceTypeEnum doris 前后缀拼接表名
            return String.format("%s%s%s", DataSourceTypeEnum.DORIS1.getTablePrefix(),tableName,DataSourceTypeEnum.DORIS1.getTableSuffix());
        }
        return String.format("%s%s%s.%s%s%s", DataSourceTypeEnum.DORIS1.getTablePrefix(),schema,DataSourceTypeEnum.DORIS1.getTableSuffix(),
                DataSourceTypeEnum.DORIS1.getTablePrefix(),  tableName,DataSourceTypeEnum.DORIS1.getTableSuffix());
    }

    @Override
    protected Pair<Character, Character> getSpecialSign() {
        return Pair.of('`', '`');
    }


    /**
     * rdbms数据预览
     *
     * @param iSource
     * @param queryDTO
     * @return
     * @throws Exception
     */
    @Override
    public List<List<Object>> getPreview(ISourceDTO iSource, SqlQueryDTO queryDTO) {
        Integer clearStatus = beforeColumnQuery(iSource, queryDTO);
        RdbmsSourceDTO rdbmsSourceDTO = (RdbmsSourceDTO) iSource;
        List<List<Object>> previewList = new ArrayList<>();
        if (StringUtils.isBlank(queryDTO.getTableName())) {
            return previewList;
        }
        Statement stmt = null;
        ResultSet rs = null;
        PreparedStatement preparedStatement = null;
        try {
            if (iSource instanceof ImpalaSourceDTO && queryDTO.getPageNum() != null) {
                String sql = String.format("SELECT * from %s ", transferSchemaAndTableName(iSource, queryDTO));
                String whereSql = dealWhereSql(rdbmsSourceDTO, sql, queryDTO);
                String impalaOrderByColumn = queryDTO.getImpalaOrderByColumn();
                if (StringUtils.isEmpty(impalaOrderByColumn)) {
                    throw new DtLoaderException("Impala数据源预览分页数据需要order by的字段");
                }
                sql = sql + whereSql + String.format(" order by `%s` limit ? offset ?", impalaOrderByColumn);

                preparedStatement = rdbmsSourceDTO.getConnection().prepareStatement(sql);
                Integer previewNum = queryDTO.getPreviewNum();
                Integer pageNum = queryDTO.getPageNum();
                preparedStatement.setInt(1, previewNum);
                preparedStatement.setInt(2, (pageNum - 1) * previewNum);
                rs = preparedStatement.executeQuery();
            } else {
                stmt = rdbmsSourceDTO.getConnection().createStatement(ResultSet.TYPE_SCROLL_INSENSITIVE, ResultSet.CONCUR_READ_ONLY);
                //查询sql，默认预览100条
                String querySql = dealSql(rdbmsSourceDTO, queryDTO);
                String whereSql = dealWhereSql(rdbmsSourceDTO, querySql, queryDTO);
                querySql = querySql + whereSql;
                if (queryDTO.getPreviewNum() != null) {
                    int endIndex = queryDTO.getPreviewNum();
                    Integer pageNum = queryDTO.getPageNum();
                    if (pageNum != null) {
                        endIndex = pageNum * endIndex;
                    }
                    stmt.setMaxRows(endIndex);
                }
                rs = stmt.executeQuery(querySql);

                if (queryDTO.getPreviewNum() != null) {
                    int previewNum = queryDTO.getPreviewNum();
                    Integer pageNum = queryDTO.getPageNum();
                    if (pageNum != null) {
                        // 查询分页数据
                        int beginIndex = (pageNum - 1) * previewNum;
                        if (beginIndex > 0) {
                            // 设置读取数据的起始位置
                            rs.absolute(beginIndex);
                        }
                    }
                }
            }

            ResultSetMetaData rsmd = rs.getMetaData();
            //存储字段信息
            List<Object> metaDataList = Lists.newArrayList();
            //字段数量
            int len = rsmd.getColumnCount();
            for (int i = 0; i < len; i++) {
                metaDataList.add(rsmd.getColumnLabel(i + 1));
            }
            while (rs.next()) {
                //一个columnData存储一行数据信息
                ArrayList<Object> columnData = Lists.newArrayList();
                for (int i = 0; i < len; i++) {
                    String result = dealPreviewResult(rs.getObject(i + 1));
                    columnData.add(result);
                }
                previewList.add(columnData);
            }
        } catch (Exception e) {
            throw new DtLoaderException(e.getMessage(), e);
        } finally {
            DBUtil.closeDBResources(rs, stmt, DBUtil.clearAfterGetConnection(rdbmsSourceDTO, clearStatus));
            if (preparedStatement != null) {
                try {
                    preparedStatement.close();
                } catch (SQLException e) {
                    e.printStackTrace();
                }
            }
        }
        return previewList;
    }


    @Override
    public List<ColumnMetaDTO> getColumnMetaData(ISourceDTO iSource, SqlQueryDTO queryDTO) {
        Integer clearStatus = beforeColumnQuery(iSource, queryDTO);
        RdbmsSourceDTO rdbmsSourceDTO = (RdbmsSourceDTO) iSource;
        Set<ColumnMetaDTO> columns = new HashSet<>();
        List<ColumnMetaDTO> newColumns = new ArrayList<>();
        Statement statement = null;
        Statement statement1 = null;
        ResultSet rs = null;
        ResultSet rsColumn = null;
        ResultSet pkRs = null;
        ResultSet fkRs = null;
        ResultSet uniqueRs = null;
        ResultSet allIndexRs = null;
        ArrayList<String> pkList = new ArrayList<>();
        ArrayList<String> fkList = new ArrayList<>();

        // 记录 SHOW CREATE TABLE 中字段定义
        Map<String, String> columnTypeMap = new HashMap<>();

        try {
            log.info("------------------开始getColumnMetaData------------------");
            rdbmsSourceDTO.setConnection(getTransConn(rdbmsSourceDTO.getConnection()));
            DatabaseMetaData metaData = rdbmsSourceDTO.getConnection().getMetaData();

            String catalog = rdbmsSourceDTO.getConnection().getCatalog();
            if (StringUtils.isNotEmpty(catalog) && StringUtils.isNotEmpty(queryDTO.getSchema())) {
                catalog = queryDTO.getSchema();
            }

            try {
                pkRs = metaData.getPrimaryKeys(catalog, queryDTO.getSchema(), queryDTO.getTableName());
                while (pkRs.next()) {
                    pkList.add(pkRs.getString("COLUMN_NAME"));
                }
            } catch (Exception e) {
                log.error("------------------执行pkRs异常------------------" + e.getMessage());
            }

            try {
                fkRs = metaData.getExportedKeys(catalog, queryDTO.getSchema(), queryDTO.getTableName());
                while (fkRs.next()) {
                    fkList.add(fkRs.getString("PKCOLUMN_NAME"));
                }
            } catch (Exception e) {
                log.info("------------------执行fkRs异常------------------" + e.getMessage());
            }

            // 加载 SHOW CREATE TABLE
            try (Statement ddlStmt = rdbmsSourceDTO.getConnection().createStatement();
                 ResultSet ddlRs = ddlStmt.executeQuery("SHOW CREATE TABLE " + queryDTO.getSchema() + "." +queryDTO.getTableName())) {
                if (ddlRs.next()) {
                    String ddl = ddlRs.getString(2);
                    Pattern pattern = Pattern.compile("`(\\w+)`\\s+([a-zA-Z0-9(),]+)");
                    Matcher matcher = pattern.matcher(ddl);
                    while (matcher.find()) {
                        columnTypeMap.put(matcher.group(1), matcher.group(2));
                    }
                }
            } catch (Exception e) {
                log.warn("无法解析 SHOW CREATE TABLE 字段定义: {}", e.getMessage());
            }

            String sql = "SELECT COLUMN_NAME, column_default, is_nullable, DATA_TYPE, " +
                    "CHARACTER_MAXIMUM_LENGTH , NUMERIC_PRECISION , NUMERIC_SCALE , COLUMN_KEY, COLUMN_COMMENT " +
                    "FROM information_schema.COLUMNS WHERE TABLE_SCHEMA = '%s' AND TABLE_NAME = '%s'";
            sql = String.format(sql, queryDTO.getSchema(), queryDTO.getTableName());
            statement1 = rdbmsSourceDTO.getConnection().createStatement();
            rsColumn = statement1.executeQuery(sql);

            while (rsColumn.next()) {
                ColumnMetaDTO columnMetaDTO = new ColumnMetaDTO();
                String columnName = rsColumn.getString(1);
                columnMetaDTO.setKey(columnName);
                columnMetaDTO.setDefaultValue(rsColumn.getString(2));
                columnMetaDTO.setNotNullFlag("YES".equalsIgnoreCase(rsColumn.getString(3)));
                String dataType = rsColumn.getString(4);
                columnMetaDTO.setType(dataType);

                String characterMaximumLength = rsColumn.getString(5);
                if (StringUtils.isNotEmpty(characterMaximumLength)) {
                    Integer len = Integer.valueOf(characterMaximumLength);
                    columnMetaDTO.setLength(len);
                    columnMetaDTO.setPrecision(len);
                }
                //长度和精度
                String typeDef = columnTypeMap.get(columnName);
                if (typeDef != null && typeDef.matches("(?i).*\\(\\d+(,\\d+)?\\).*")) {
                    Matcher m = Pattern.compile("\\((\\d+)(?:,(\\d+))?\\)").matcher(typeDef);
                    if (m.find()) {
                        columnMetaDTO.setPrecision(Integer.parseInt(m.group(1)));
                        columnMetaDTO.setLength(Integer.parseInt(m.group(1)));
                        if (m.group(2) != null) {
                            columnMetaDTO.setScale(Integer.parseInt(m.group(2)));
                        }
                    }
                }


                columnMetaDTO.setComment(rsColumn.getString(9));
                columnMetaDTO.setPkflag(pkList.contains(columnName));
                columnMetaDTO.setFkflag(fkList.contains(columnName));
                columns.add(columnMetaDTO);
            }

            statement = rdbmsSourceDTO.getConnection().createStatement();
            statement.setMaxRows(1);
            String queryColumnSql = "select " + CollectionUtil.listToStr(queryDTO.getColumns()) +
                    " from " + transferSchemaAndTableName(rdbmsSourceDTO, queryDTO) + " where 1=2";
            rs = statement.executeQuery(queryColumnSql);

            ResultSetMetaData rsMetaData = rs.getMetaData();
            int columnCount = rsMetaData.getColumnCount();
            for (int i = 1; i <= columnCount; i++) {
                String columnName = rsMetaData.getColumnName(i);
                for (ColumnMetaDTO columnMetaDTO : columns) {
                    if (columnMetaDTO.getKey().equals(columnName)) {
                        columnMetaDTO.setDateType(rsMetaData.getColumnClassName(i));
                        String type = columnMetaDTO.getType();
                        int columnType = rsMetaData.getColumnType(i);
                        if ("array".equalsIgnoreCase(type) || "map".equalsIgnoreCase(type) || "json".equalsIgnoreCase(type)) {
                            columnType = Types.LONGVARCHAR;
                        }
                        columnMetaDTO.setDataType(columnType);
                        newColumns.add(columnMetaDTO);
                    }
                }
            }
        } catch (SQLException e) {
            log.error("------------------表{}，字段采集报错{}------------------", queryDTO.getTableName(), e.getMessage());
            if (e.getMessage().contains(DONT_EXIST)) {
                throw new DtLoaderException(String.format(queryDTO.getTableName() + "table not exist,%s", e.getMessage()), e);
            } else {
                throw new DtLoaderException(String.format("Failed to get the meta information of the fields of the table: %s. Please contact the DBA to check the database and table information: %s",
                        queryDTO.getTableName(), e.getMessage()), e);
            }
        } finally {
            DBUtil.closeDBResources(pkRs, null, null);
            DBUtil.closeDBResources(fkRs, null, null);
            DBUtil.closeDBResources(uniqueRs, null, null);
            DBUtil.closeDBResources(allIndexRs, null, null);
            DBUtil.closeDBResources(rsColumn, null, DBUtil.clearAfterGetConnection(rdbmsSourceDTO, clearStatus));
            DBUtil.closeDBResources(rs, statement, DBUtil.clearAfterGetConnection(rdbmsSourceDTO, clearStatus));
        }
        return newColumns;
    }


    @Override
    public List<DbTableVO> getFunctionList(ISourceDTO sourceDTO, SqlQueryDTO queryDTO){
        Mysql5SourceDTO mysql5SourceDTO = (Mysql5SourceDTO) sourceDTO;
        Integer clearStatus = beforeQuery(sourceDTO, queryDTO, false);
        Statement statement = null;
        ResultSet resultSet = null;
        List<DbTableVO> indexMetaDTOS = new ArrayList<>();
        try {
            statement = mysql5SourceDTO.getConnection().createStatement();
            //判断搜查类型为空时查全部
            String sql = "";
            //type 为函数还是存储过程
            if(SqlConstants.FUNCTION_TYPE.equalsIgnoreCase(queryDTO.getType())){
                sql = String.format(SqlConstants.GET_PRODUCE_SQL, queryDTO.getDbName(),SqlConstants.FUNCTION_TYPE);
            }

            if(com.dtstack.dtcenter.common.loader.mysql5.SqlConstants.PROCEDURE_TYPE.equalsIgnoreCase(queryDTO.getType())){
                sql = String.format(SqlConstants.GET_PRODUCE_SQL, queryDTO.getDbName(), SqlConstants.PROCEDURE_TYPE);
            }
            if(StringUtils.isNotEmpty(queryDTO.getTableNamePattern())){
                String format = String.format(SqlConstants.SEARCH_PRODUCE_SQL, queryDTO.getTableNamePattern());
                sql = sql + format;
            }

            log.info("getFunctionList SQl:"+sql);
            resultSet = statement.executeQuery(sql);
            while (resultSet.next()) {
                DbTableVO indexMetaDTO = new DbTableVO();
                indexMetaDTO.setDbName(resultSet.getString("ROUTINE_SCHEMA"));
                indexMetaDTO.setName(resultSet.getString("ROUTINE_NAME"));
                String indexType = resultSet.getString("ROUTINE_TYPE");
                indexMetaDTO.setType(indexType);
                indexMetaDTO.setComment(resultSet.getString("ROUTINE_COMMENT"));
                indexMetaDTO.setDdl(resultSet.getString("ROUTINE_DEFINITION"));
                indexMetaDTO.setCharset(resultSet.getString("CHARACTER_SET_CLIENT"));
                indexMetaDTO.setCollation(resultSet.getString("COLLATION_CONNECTION"));
                indexMetaDTO.setExternalLanguage(resultSet.getString("EXTERNAL_LANGUAGE"));
                indexMetaDTOS.add(indexMetaDTO);
            }
        } catch (Exception e) {
            throw new DtLoaderException(String.format("根据指定库获取函数或者存储过程异常:{}"), e);
        } finally {
            DBUtil.closeDBResources(resultSet, statement, DBUtil.clearAfterGetConnection(mysql5SourceDTO, clearStatus));
        }
        return indexMetaDTOS;
    }
    @Override
    public List<ColumnMetaDTO> getFunctionArguments(ISourceDTO sourceDTO, SqlQueryDTO queryDTO){
        Mysql5SourceDTO mysql5SourceDTO = (Mysql5SourceDTO) sourceDTO;
        Integer clearStatus = beforeQuery(sourceDTO, queryDTO, false);
        Statement statement = null;
        ResultSet resultSet = null;
        List<ColumnMetaDTO> indexCols = new ArrayList<>();
        try {
            statement = mysql5SourceDTO.getConnection().createStatement();
            //判断搜查类型为空时查全部
            String  sql = String.format(SqlConstants.GET_PRODUCE_ARGUMENTS_SQL, queryDTO.getDbName(),queryDTO.getObjectName()).toLowerCase();
           log.info("getFunctionArguments SQl:"+sql);
            resultSet = statement.executeQuery(sql);
            while (resultSet.next()) {
                ColumnMetaDTO indexMetaDTO = new ColumnMetaDTO();
                String parameterName = resultSet.getString("PARAMETER_NAME");
                if(StringUtils.isEmpty(parameterName)){
                    parameterName="RETURN";
                }
                indexMetaDTO.setKey(parameterName);
                indexMetaDTO.setType(resultSet.getString("DATA_TYPE"));
                String parameterMode = resultSet.getString("PARAMETER_MODE");
                if(StringUtils.isEmpty(parameterMode)){
                    parameterMode="RETURN";
                }
                indexMetaDTO.setInOut(parameterMode);
                indexMetaDTO.setColumnOrder(resultSet.getInt("ORDINAL_POSITION"));
                int characterOctetLength = resultSet.getInt("CHARACTER_OCTET_LENGTH");
                int numericPrecision = resultSet.getInt("NUMERIC_PRECISION");
                if(characterOctetLength>0){
                    indexMetaDTO.setLength(characterOctetLength);
                }else{
                    indexMetaDTO.setLength(numericPrecision);
                    indexMetaDTO.setPrecision(numericPrecision);
                    indexMetaDTO.setScale(resultSet.getInt("NUMERIC_SCALE"));
                }
                indexCols.add(indexMetaDTO);
            }
        } catch (Exception e) {
            throw new DtLoaderException(String.format("根据指定库获取函数或者存储过程异常:{}"), e);
        } finally {
            DBUtil.closeDBResources(resultSet, statement, DBUtil.clearAfterGetConnection(mysql5SourceDTO, clearStatus));
        }
        return indexCols;
    }

    @Override
    public String getTimeZoneByDatabase(ISourceDTO source, SqlQueryDTO queryDTO) {
        Mysql5SourceDTO mysql5SourceDTO = (Mysql5SourceDTO) source;
        Integer clearStatus = beforeQuery(source,queryDTO,false);
        Statement statement = null;
        ResultSet resultSet = null;
        try {
            statement = mysql5SourceDTO.getConnection().createStatement();
            resultSet = statement.executeQuery("SHOW VARIABLES LIKE '%time_zone%'");
            while (resultSet.next()) {
                String timeZone = resultSet.getString(1);
                String timeZoneValue = resultSet.getString(2);
                if("system_time_zone".equalsIgnoreCase(timeZone)){
                    return timeZoneValue;
                }

            }
        } catch (Exception e) {
            throw new DtLoaderException(String.format("get time_zone:  information error. Please contact the DBA to check the database、table information."), e);
        } finally {
            DBUtil.closeDBResources(resultSet, statement, DBUtil.clearAfterGetConnection(mysql5SourceDTO, clearStatus));
        }
        return "";
    }
}
